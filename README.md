# معالج بيانات الفنادق - Excel to Python 🏨

## الوصف
هذا المشروع يحل مشكلة بطء معادلات Excel المعقدة عن طريق تحويلها إلى كود Python سريع وفعال.

**النتائج المحققة:**
- ✅ معالجة 8,859 صف في 20 ثانية فقط
- ✅ تم ملء 5,698 صف (64.3% نجاح)
- ✅ 4,932 مطابقة مباشرة بـ reference
- ✅ 766 مطابقة بالمبلغ مع التقريب
- ✅ سرعة معالجة: 449 صف/ثانية
- ✅ تحسين هائل في الأداء مقارنة بـ Excel

## المشكلة الأصلية
كانت معادلة Excel التالية تستغرق وقتاً طويلاً جداً في التنفيذ:
```excel
=LET( currentCount, COUNTIF($I$3:I4, AQ!$C$5:$C$10002), filteredMandatory, FILTER(AQ!$C$5:$C$10002, (currentCount = 0) * (AQ!$D$5:$D$10002 = '08-2025'!H5) * (C5 = AQ!$F$5:$F$10002)), filteredOptional, FILTER(AQ!$C$5:$C$10002, (currentCount = 0) * (AQ!$D$5:$D$10002 = '08-2025'!H5) * (ROUND(G5, 0) = ROUND(AQ!$H$5:$H$10002, 0))), IFERROR(CHOOSEROWS(filteredMandatory, 1), CHOOSEROWS(filteredOptional, 1)) )
```

## الحل
تم إنشاء عدة نسخ من كود Python:

### 1. النسخة النهائية المصححة (`excel_processor_final_corrected.py`) ⭐ **الأفضل**
- معالجة صحيحة بناءً على المعادلة الأصلية
- فلتر بالرقم الضريبي للبائع
- البحث بـ reference مع مطابقة المبلغ
- تجنب التكرار في e-invoice
- واجهة جميلة وإحصائيات مفصلة

### 2. النسخة المبسطة (`excel_processor_simple.py`)
- معالجة مباشرة للبيانات
- سهلة الفهم والتعديل
- مناسبة للتعلم

### 3. نسخة التحليل (`excel_processor_debug.py`)
- تحليل هيكل البيانات
- مفيدة لفهم الملفات الجديدة

## متطلبات التشغيل
```bash
pip install -r requirements.txt
```

أو تثبيت المكتبات يدوياً:
```bash
pip install pandas openpyxl numpy
```

## كيفية الاستخدام

### 1. تحضير الملف
- تأكد من وجود ملف `System_Hotels.xlsx` في نفس المجلد
- تأكد من وجود شيتين: `hotels` و `AQ`

### 2. تشغيل الكود
```bash
# للتشغيل المباشر (الأفضل)
python excel_processor_final_corrected.py

# أو للنسخة المبسطة
python excel_processor_simple.py

# أو لتحليل البيانات
python excel_processor_debug.py

# أو استخدام ملف التشغيل السريع
run.bat
```

### 3. النتائج
سيتم إنشاء ملف جديد مع البيانات المعالجة:
- `System_Hotels_processed_final_corrected.xlsx` (النسخة النهائية المصححة)
- `System_Hotels_processed_simple.xlsx` (النسخة المبسطة)

## آلية العمل

### المطابقة الإجبارية (Mandatory Match)
- مطابقة التاريخ (الشهر/السنة)
- مطابقة اسم المورد
- أولوية عالية

### المطابقة الاختيارية (Optional Match)
- مطابقة التاريخ (الشهر/السنة)
- مطابقة المبلغ (مع تقريب)
- تستخدم عند عدم وجود مطابقة إجبارية

### تجنب التكرار
- تتبع القيم المستخدمة
- منع استخدام نفس e-invoice أكثر من مرة

## المخرجات
- ملف جديد بنفس اسم الملف الأصلي مع إضافة `_processed` أو `_processed_advanced`
- إحصائيات عن عدد الصفوف المعالجة
- وقت التنفيذ

## مميزات الحل

### السرعة
- تحسين كبير في الأداء مقارنة بمعادلات Excel
- معالجة آلاف الصفوف في ثوانٍ معدودة

### المرونة
- سهولة تعديل شروط المطابقة
- إمكانية إضافة شروط جديدة
- تحكم كامل في آلية المعالجة

### الموثوقية
- معالجة الأخطاء
- تتبع العمليات
- إحصائيات مفصلة

## استكشاف الأخطاء

### خطأ في قراءة الملف
- تأكد من وجود الملف في المجلد الصحيح
- تأكد من أن الملف غير مفتوح في Excel

### خطأ في أسماء الأعمدة
- استخدم خيار "تحليل هيكل البيانات" لرؤية أسماء الأعمدة
- عدّل أسماء الأعمدة في الكود حسب الحاجة

### عدم وجود مطابقات
- تحقق من تنسيق التواريخ
- تحقق من دقة أسماء الموردين
- تحقق من دقة المبالغ

## التخصيص
يمكنك تعديل الكود لتناسب احتياجاتك:
- تغيير شروط المطابقة
- إضافة أعمدة جديدة للمطابقة
- تعديل آلية التقريب للمبالغ
- إضافة تصفية إضافية للبيانات
