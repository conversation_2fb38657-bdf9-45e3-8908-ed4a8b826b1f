import pandas as pd
import numpy as np
import random

def comprehensive_test_cases(file_path):
    """
    فحص شامل لحالات متنوعة من البيانات
    """
    print("🔍 فحص شامل للحالات المتنوعة")
    print("=" * 60)
    
    # قراءة البيانات الأصلية
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=3, header=0)
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=3, header=0)
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()
    
    # إزالة الصفوف الفارغة من AQ
    aq_df = aq_df.dropna(subset=['e-invoice'])
    
    # قراءة النتائج المعالجة
    try:
        import glob
        processed_files = glob.glob("System_Hotels_processed_final_corrected_*.xlsx")
        if processed_files:
            latest_file = max(processed_files)
            print(f"📖 قراءة الملف المعالج: {latest_file}")
            processed_df = pd.read_excel(latest_file, sheet_name='hotels', skiprows=2, header=0)
            processed_df.columns = processed_df.columns.str.strip()
        else:
            print("❌ لم يتم العثور على ملف معالج")
            return
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف المعالج: {e}")
        return
    
    print(f"✅ تم قراءة {len(hotels_df)} صف من hotels و {len(aq_df)} صف من AQ")
    
    # 1. فحص حالات المطابقة الناجحة
    print(f"\n🎯 1. تحليل الحالات الناجحة:")
    print("-" * 40)
    
    successful_matches = processed_df[
        (processed_df['e-invoice'].notna()) & 
        (processed_df['e-invoice'].astype(str) != 'nan') & 
        (processed_df['e-invoice'].astype(str) != '')
    ]
    
    print(f"إجمالي المطابقات الناجحة: {len(successful_matches):,}")
    
    # عينة عشوائية من المطابقات الناجحة
    sample_successful = successful_matches.sample(n=min(5, len(successful_matches)), random_state=42)
    
    for i, (idx, row) in enumerate(sample_successful.iterrows()):
        reference = row.get('reference', 'N/A')
        tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
        amount = row.get('TOTAL_COST', 'N/A')
        einvoice = row.get('e-invoice', 'N/A')
        supplier = row.get('SUPPLIERNAME', 'N/A')
        
        print(f"\n  ✅ حالة ناجحة #{i+1}:")
        print(f"     Reference: {reference}")
        print(f"     المورد: {supplier}")
        print(f"     الرقم الضريبي: {tax_num}")
        print(f"     المبلغ: {amount}")
        print(f"     e-invoice: {einvoice}")
        
        # التحقق من نوع المطابقة
        if pd.notna(tax_num):
            tax_num_str = str(int(float(tax_num)))
        else:
            tax_num_str = ''
        
        # البحث في AQ
        aq_matches = aq_df[
            (aq_df['الرقم الضريبى للبائع'].astype(str) == tax_num_str) &
            (aq_df['e-invoice'].astype(str) == str(einvoice))
        ]
        
        if len(aq_matches) > 0:
            aq_row = aq_matches.iloc[0]
            aq_ref = aq_row.get('reference', 'N/A')
            aq_amount = aq_row.get('TOTAL_COST', 'N/A')
            
            print(f"     مطابق في AQ:")
            print(f"       Reference: {aq_ref}")
            print(f"       المبلغ: {aq_amount}")
            
            # تحديد نوع المطابقة
            ref_match = str(reference) == str(aq_ref)
            if pd.notna(amount) and pd.notna(aq_amount):
                amount_diff = abs(float(amount) - float(aq_amount))
                max_allowed_diff = max(5.0, float(amount) * 0.05)
                max_allowed_diff = min(max_allowed_diff, 50.0)
                amount_match = amount_diff <= max_allowed_diff
            else:
                amount_match = False
                amount_diff = 'N/A'
            
            if ref_match and amount_match:
                match_type = "🥇 المرحلة 1 (رقم ضريبي + reference + مبلغ)"
            elif ref_match:
                match_type = "🥈 المرحلة 2 (رقم ضريبي + reference)"
            elif amount_match:
                match_type = "🥉 المرحلة 3 (رقم ضريبي + مبلغ)"
            else:
                match_type = "❓ غير محدد"
            
            print(f"       نوع المطابقة: {match_type}")
            if amount_diff != 'N/A':
                print(f"       فرق المبلغ: {amount_diff:.2f}")
    
    # 2. فحص حالات عدم المطابقة
    print(f"\n❌ 2. تحليل الحالات غير الناجحة:")
    print("-" * 40)
    
    failed_matches = processed_df[
        (processed_df['e-invoice'].isna()) | 
        (processed_df['e-invoice'].astype(str) == 'nan') | 
        (processed_df['e-invoice'].astype(str) == '')
    ]
    
    print(f"إجمالي الحالات غير الناجحة: {len(failed_matches):,}")
    
    # عينة عشوائية من الحالات غير الناجحة
    sample_failed = failed_matches.sample(n=min(5, len(failed_matches)), random_state=42)
    
    for i, (idx, row) in enumerate(sample_failed.iterrows()):
        reference = row.get('reference', 'N/A')
        tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
        amount = row.get('TOTAL_COST', 'N/A')
        supplier = row.get('SUPPLIERNAME', 'N/A')
        
        print(f"\n  ❌ حالة غير ناجحة #{i+1}:")
        print(f"     Reference: {reference}")
        print(f"     المورد: {supplier}")
        print(f"     الرقم الضريبي: {tax_num}")
        print(f"     المبلغ: {amount}")
        
        # تحليل سبب الفشل
        if pd.notna(tax_num):
            tax_num_str = str(int(float(tax_num)))
        else:
            tax_num_str = ''
            print(f"       ❌ سبب الفشل: رقم ضريبي مفقود")
            continue
        
        # البحث في AQ بالرقم الضريبي
        aq_tax_matches = aq_df[aq_df['الرقم الضريبى للبائع'].astype(str) == tax_num_str]
        print(f"     مطابقات بالرقم الضريبي في AQ: {len(aq_tax_matches)}")
        
        if len(aq_tax_matches) == 0:
            print(f"       ❌ سبب الفشل: لا توجد مطابقات بالرقم الضريبي")
            continue
        
        # البحث بـ reference
        aq_ref_matches = aq_tax_matches[aq_tax_matches['reference'].astype(str) == str(reference)]
        print(f"     مطابقات بـ reference: {len(aq_ref_matches)}")
        
        if len(aq_ref_matches) == 0:
            print(f"       ⚠️ لا توجد مطابقة بـ reference")
            
            # البحث بالمبلغ
            if pd.notna(amount):
                max_allowed_diff = max(5.0, float(amount) * 0.05)
                max_allowed_diff = min(max_allowed_diff, 50.0)
                
                amount_matches = []
                for _, aq_row in aq_tax_matches.iterrows():
                    aq_amount = pd.to_numeric(aq_row.get('TOTAL_COST', 0), errors='coerce')
                    if pd.notna(aq_amount):
                        amount_diff = abs(float(amount) - float(aq_amount))
                        if amount_diff <= max_allowed_diff:
                            amount_matches.append((aq_row, amount_diff))
                
                print(f"       مطابقات بالمبلغ (ضمن {max_allowed_diff:.2f}): {len(amount_matches)}")
                
                if len(amount_matches) == 0:
                    print(f"       ❌ سبب الفشل: لا توجد مطابقة بالمبلغ")
                    # عرض أقرب المبالغ
                    closest_amounts = []
                    for _, aq_row in aq_tax_matches.head(3).iterrows():
                        aq_amount = pd.to_numeric(aq_row.get('TOTAL_COST', 0), errors='coerce')
                        if pd.notna(aq_amount):
                            diff = abs(float(amount) - float(aq_amount))
                            closest_amounts.append((aq_amount, diff))
                    
                    if closest_amounts:
                        closest_amounts.sort(key=lambda x: x[1])
                        print(f"       أقرب المبالغ:")
                        for j, (aq_amt, diff) in enumerate(closest_amounts[:3]):
                            print(f"         {j+1}. {aq_amt} (فرق: {diff:.2f})")
        else:
            # يوجد reference لكن لم يتم المطابقة - فحص المبلغ
            aq_row = aq_ref_matches.iloc[0]
            aq_amount = pd.to_numeric(aq_row.get('TOTAL_COST', 0), errors='coerce')
            
            if pd.notna(amount) and pd.notna(aq_amount):
                amount_diff = abs(float(amount) - float(aq_amount))
                max_allowed_diff = max(5.0, float(amount) * 0.05)
                max_allowed_diff = min(max_allowed_diff, 50.0)
                
                print(f"       فرق المبلغ: {amount_diff:.2f}")
                print(f"       الحد الأقصى المسموح: {max_allowed_diff:.2f}")
                
                if amount_diff > max_allowed_diff:
                    print(f"       ❌ سبب الفشل: فرق المبلغ كبير جداً")
                else:
                    print(f"       ❓ سبب غير واضح - يجب أن تنجح المطابقة")
    
    # 3. إحصائيات شاملة
    print(f"\n📊 3. إحصائيات شاملة:")
    print("-" * 30)
    
    total_rows = len(processed_df)
    successful_rows = len(successful_matches)
    failed_rows = len(failed_matches)
    success_rate = (successful_rows / total_rows) * 100
    
    print(f"إجمالي الصفوف: {total_rows:,}")
    print(f"المطابقات الناجحة: {successful_rows:,} ({success_rate:.1f}%)")
    print(f"المطابقات الفاشلة: {failed_rows:,} ({100-success_rate:.1f}%)")
    
    # تحليل أسباب الفشل
    print(f"\n📈 4. تحليل أسباب الفشل:")
    print("-" * 30)
    
    # عينة أكبر للتحليل
    sample_size = min(100, len(failed_matches))
    large_sample_failed = failed_matches.sample(n=sample_size, random_state=42)
    
    failure_reasons = {
        'no_tax_number': 0,
        'no_tax_match_in_aq': 0,
        'no_reference_match': 0,
        'amount_diff_too_large': 0,
        'unknown': 0
    }
    
    for _, row in large_sample_failed.iterrows():
        tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
        reference = row.get('reference', 'N/A')
        amount = row.get('TOTAL_COST', 'N/A')
        
        if pd.isna(tax_num):
            failure_reasons['no_tax_number'] += 1
            continue
        
        tax_num_str = str(int(float(tax_num)))
        aq_tax_matches = aq_df[aq_df['الرقم الضريبى للبائع'].astype(str) == tax_num_str]
        
        if len(aq_tax_matches) == 0:
            failure_reasons['no_tax_match_in_aq'] += 1
            continue
        
        aq_ref_matches = aq_tax_matches[aq_tax_matches['reference'].astype(str) == str(reference)]
        
        if len(aq_ref_matches) == 0:
            # فحص المطابقة بالمبلغ
            if pd.notna(amount):
                max_allowed_diff = max(5.0, float(amount) * 0.05)
                max_allowed_diff = min(max_allowed_diff, 50.0)
                
                found_amount_match = False
                for _, aq_row in aq_tax_matches.iterrows():
                    aq_amount = pd.to_numeric(aq_row.get('TOTAL_COST', 0), errors='coerce')
                    if pd.notna(aq_amount):
                        amount_diff = abs(float(amount) - float(aq_amount))
                        if amount_diff <= max_allowed_diff:
                            found_amount_match = True
                            break
                
                if not found_amount_match:
                    failure_reasons['no_reference_match'] += 1
                else:
                    failure_reasons['unknown'] += 1
            else:
                failure_reasons['no_reference_match'] += 1
        else:
            failure_reasons['amount_diff_too_large'] += 1
    
    print(f"تحليل {sample_size} حالة فاشلة:")
    print(f"  • رقم ضريبي مفقود: {failure_reasons['no_tax_number']} ({failure_reasons['no_tax_number']/sample_size*100:.1f}%)")
    print(f"  • لا توجد مطابقة بالرقم الضريبي: {failure_reasons['no_tax_match_in_aq']} ({failure_reasons['no_tax_match_in_aq']/sample_size*100:.1f}%)")
    print(f"  • لا توجد مطابقة بـ reference أو المبلغ: {failure_reasons['no_reference_match']} ({failure_reasons['no_reference_match']/sample_size*100:.1f}%)")
    print(f"  • فرق المبلغ كبير جداً: {failure_reasons['amount_diff_too_large']} ({failure_reasons['amount_diff_too_large']/sample_size*100:.1f}%)")
    print(f"  • أسباب غير واضحة: {failure_reasons['unknown']} ({failure_reasons['unknown']/sample_size*100:.1f}%)")

def main():
    file_path = "System_Hotels.xlsx"
    comprehensive_test_cases(file_path)

if __name__ == "__main__":
    main()
