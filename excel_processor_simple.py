import pandas as pd
import numpy as np
import time
from collections import defaultdict

def process_hotels_data_simple(file_path):
    """
    معالجة بيانات الفنادق بشكل مباشر
    """
    print("جاري قراءة ملف Excel...")

    # قراءة الشيتين مع تحديد الرؤوس الصحيحة
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=3, header=0)
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=3, header=0)
    
    print(f"تم قراءة {len(hotels_df)} صف من شيت hotels")
    print(f"تم قراءة {len(aq_df)} صف من شيت AQ")
    
    # طباعة أسماء الأعمدة للتأكد
    print("\nأعمدة شيت hotels:")
    for i, col in enumerate(hotels_df.columns):
        print(f"  {i}: {col}")
    
    print("\nأعمدة شيت AQ:")
    for i, col in enumerate(aq_df.columns):
        print(f"  {i}: {col}")
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()
    
    # إزالة الصفوف الفارغة من AQ
    aq_df = aq_df.dropna(subset=['e-invoice'])  # عمود e-invoice
    
    print(f"\nبعد تنظيف البيانات: {len(aq_df)} صف في شيت AQ")
    
    # إنشاء عمود e-invoice إذا لم يكن موجوداً
    if 'e-invoice' not in hotels_df.columns:
        hotels_df['e-invoice'] = ''
    
    print("\nجاري إنشاء فهارس للبحث السريع...")
    
    # إنشاء فهارس للبحث السريع
    mandatory_index = defaultdict(list)
    optional_index = defaultdict(list)
    
    for idx, row in aq_df.iterrows():
        einvoice = row['e-invoice']  # عمود e-invoice
        date_str = str(row['تاريخ الإصدار'])[:7] if pd.notna(row['تاريخ الإصدار']) else ''  # عمود التاريخ
        supplier = str(row['إسم البائع']) if pd.notna(row['إسم البائع']) else ''  # عمود المورد
        amount = pd.to_numeric(row['اجمالى الفاتورة بالعملة'], errors='coerce')  # عمود المبلغ
        
        if pd.notna(amount):
            amount_rounded = round(amount)
        else:
            amount_rounded = 0
        
        # إضافة للفهرس الإجباري (تاريخ + مورد)
        mandatory_key = f"{date_str}_{supplier}"
        mandatory_index[mandatory_key].append((idx, einvoice))
        
        # إضافة للفهرس الاختياري (تاريخ + مبلغ)
        optional_key = f"{date_str}_{amount_rounded}"
        optional_index[optional_key].append((idx, einvoice))
    
    print(f"تم إنشاء {len(mandatory_index)} مفتاح للمطابقة الإجبارية")
    print(f"تم إنشاء {len(optional_index)} مفتاح للمطابقة الاختيارية")
    
    print("\nجاري معالجة البيانات...")
    start_time = time.time()
    
    used_einvoices = set()
    matched_count = 0
    mandatory_matches = 0
    optional_matches = 0
    
    for idx, hotel_row in hotels_df.iterrows():
        # تخطي الصفوف التي لديها e-invoice بالفعل
        current_einvoice = hotels_df.at[idx, 'e-invoice']
        if pd.notna(current_einvoice) and str(current_einvoice).strip() != '':
            used_einvoices.add(str(current_einvoice).strip())
            continue
        
        # استخراج بيانات الفندق
        hotel_date = str(hotel_row.get('CheckOut', ''))[:7] if pd.notna(hotel_row.get('CheckOut', '')) else ''
        hotel_supplier = str(hotel_row.get('SUPPLIERNAME', '')) if pd.notna(hotel_row.get('SUPPLIERNAME', '')) else ''
        hotel_amount = pd.to_numeric(hotel_row.get('TOTAL_COST', 0), errors='coerce')
        
        if pd.isna(hotel_amount):
            hotel_amount = 0
        
        hotel_amount_rounded = round(hotel_amount)
        
        # البحث في الفهرس الإجباري أولاً
        mandatory_key = f"{hotel_date}_{hotel_supplier}"
        found_match = False
        
        if mandatory_key in mandatory_index:
            for _, einvoice in mandatory_index[mandatory_key]:
                if str(einvoice).strip() not in used_einvoices:
                    hotels_df.at[idx, 'e-invoice'] = einvoice
                    used_einvoices.add(str(einvoice).strip())
                    matched_count += 1
                    mandatory_matches += 1
                    found_match = True
                    break
        
        # إذا لم نجد مطابقة إجبارية، ابحث في الفهرس الاختياري
        if not found_match:
            optional_key = f"{hotel_date}_{hotel_amount_rounded}"
            if optional_key in optional_index:
                for _, einvoice in optional_index[optional_key]:
                    if str(einvoice).strip() not in used_einvoices:
                        hotels_df.at[idx, 'e-invoice'] = einvoice
                        used_einvoices.add(str(einvoice).strip())
                        matched_count += 1
                        optional_matches += 1
                        break
    
    processing_time = time.time() - start_time
    print(f"تم الانتهاء من المعالجة في {processing_time:.2f} ثانية")
    print(f"تم العثور على {matched_count} مطابقة")
    print(f"  - مطابقات إجبارية: {mandatory_matches}")
    print(f"  - مطابقات اختيارية: {optional_matches}")
    
    # حفظ النتائج
    output_file = file_path.replace('.xlsx', '_processed_simple.xlsx')
    
    print("جاري حفظ النتائج...")
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # حفظ البيانات مع الاحتفاظ بالتنسيق الأصلي
        hotels_df.to_excel(writer, sheet_name='hotels', index=False, startrow=2)
        aq_df.to_excel(writer, sheet_name='AQ', index=False, startrow=2)
    
    print(f"تم حفظ الملف المعالج: {output_file}")
    
    # إحصائيات مفصلة
    filled_count = (hotels_df['e-invoice'].astype(str).str.strip() != '').sum()
    total_count = len(hotels_df)
    print(f"تم ملء {filled_count} من أصل {total_count} صف ({filled_count/total_count*100:.1f}%)")
    
    return hotels_df, aq_df

def main():
    """
    الدالة الرئيسية
    """
    file_path = "System_Hotels.xlsx"
    
    try:
        print("بدء معالجة بيانات الفنادق...")
        hotels_df, aq_df = process_hotels_data_simple(file_path)
        print("\n" + "="*50)
        print("تم الانتهاء بنجاح!")
        print("="*50)
        
    except Exception as e:
        print(f"حدث خطأ: {str(e)}")
        import traceback
        traceback.print_exc()
        print("\nتأكد من:")
        print("1. وجود ملف System_Hotels.xlsx")
        print("2. وجود شيتين بأسماء 'hotels' و 'AQ'")
        print("3. تثبيت المكتبات المطلوبة: pip install pandas openpyxl")

if __name__ == "__main__":
    main()
