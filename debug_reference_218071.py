import pandas as pd
import numpy as np

def debug_reference_218071(file_path):
    """
    تحليل مفصل لـ reference 218071 لفهم المشكلة
    """
    print("🔍 تحليل مفصل لـ reference 218071")
    print("=" * 60)
    
    # قراءة البيانات
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=3, header=0)
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=3, header=0)
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()
    
    # إزالة الصفوف الفارغة من AQ
    aq_df = aq_df.dropna(subset=['e-invoice'])
    
    print(f"تم قراءة {len(hotels_df)} صف من hotels و {len(aq_df)} صف من AQ")
    
    # البحث عن reference 218071
    reference = "218071"
    
    print(f"\n🔍 البحث عن reference: {reference}")
    print("-" * 40)
    
    # في شيت hotels
    hotels_matches = hotels_df[hotels_df['reference'].astype(str) == reference]
    print(f"📋 في شيت hotels: تم العثور على {len(hotels_matches)} صف")
    
    if len(hotels_matches) > 0:
        for i, (idx, row) in enumerate(hotels_matches.iterrows()):
            tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
            amount = row.get('TOTAL_COST', 'N/A')
            einvoice = row.get('e-invoice', 'N/A')
            supplier = row.get('SUPPLIERNAME', 'N/A')
            
            print(f"  الصف {i+1}:")
            print(f"    الرقم الضريبي: {tax_num}")
            print(f"    المورد: {supplier}")
            print(f"    المبلغ: {amount}")
            print(f"    e-invoice الحالي: '{einvoice}'")
            
            # تحويل الرقم الضريبي للمطابقة
            if pd.notna(tax_num):
                tax_num_str = str(int(float(tax_num)))
            else:
                tax_num_str = ''
            
            print(f"    الرقم الضريبي المحول: {tax_num_str}")
    
    # في شيت AQ
    print(f"\n📋 في شيت AQ: البحث عن reference={reference}")
    aq_ref_matches = aq_df[aq_df['reference'].astype(str) == reference]
    print(f"تم العثور على {len(aq_ref_matches)} صف")
    
    if len(aq_ref_matches) > 0:
        for i, (idx, row) in enumerate(aq_ref_matches.iterrows()):
            tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
            amount = row.get('TOTAL_COST', 'N/A')
            einvoice = row.get('e-invoice', 'N/A')
            supplier = row.get('إسم البائع', 'N/A')
            
            print(f"  الصف {i+1}:")
            print(f"    الرقم الضريبي: {tax_num}")
            print(f"    البائع: {supplier}")
            print(f"    المبلغ: {amount}")
            print(f"    e-invoice: '{einvoice}'")
    
    # تحليل المطابقة المتوقعة
    if len(hotels_matches) > 0 and len(aq_ref_matches) > 0:
        print(f"\n🔍 تحليل المطابقة المتوقعة:")
        print("-" * 30)
        
        hotel_row = hotels_matches.iloc[0]
        hotel_tax_raw = hotel_row.get('الرقم الضريبى للبائع', '')
        if pd.notna(hotel_tax_raw):
            hotel_tax_number = str(int(float(hotel_tax_raw)))
        else:
            hotel_tax_number = ''
        hotel_amount = pd.to_numeric(hotel_row.get('TOTAL_COST', 0), errors='coerce')
        
        print(f"Hotels - رقم ضريبي: {hotel_tax_number}, مبلغ: {hotel_amount}")
        
        # البحث في AQ بنفس الرقم الضريبي
        matching_tax_aq = aq_ref_matches[aq_ref_matches['الرقم الضريبى للبائع'].astype(str) == hotel_tax_number]
        print(f"AQ - مطابقات بنفس الرقم الضريبي: {len(matching_tax_aq)}")
        
        if len(matching_tax_aq) > 0:
            for i, (idx, row) in enumerate(matching_tax_aq.iterrows()):
                aq_amount = pd.to_numeric(row.get('TOTAL_COST', 0), errors='coerce')
                aq_einvoice = row.get('e-invoice', 'N/A')
                
                if pd.notna(hotel_amount) and pd.notna(aq_amount):
                    amount_diff = abs(float(hotel_amount) - float(aq_amount))
                else:
                    amount_diff = 'N/A'
                
                print(f"  المطابقة {i+1}: مبلغ={aq_amount}, فرق المبلغ={amount_diff}, e-invoice='{aq_einvoice}'")
        
        # البحث في AQ بنفس المبلغ (تقريبي)
        if pd.notna(hotel_amount):
            max_allowed_diff = max(5.0, hotel_amount * 0.05)
            max_allowed_diff = min(max_allowed_diff, 50.0)
            
            amount_matches = []
            for idx, row in aq_ref_matches.iterrows():
                aq_amount = pd.to_numeric(row.get('TOTAL_COST', 0), errors='coerce')
                if pd.notna(aq_amount):
                    amount_diff = abs(float(hotel_amount) - float(aq_amount))
                    if amount_diff <= max_allowed_diff:
                        amount_matches.append((idx, row, amount_diff))
            
            print(f"\nمطابقات المبلغ (ضمن الحد المسموح {max_allowed_diff:.2f}): {len(amount_matches)}")
            for i, (idx, row, diff) in enumerate(amount_matches):
                aq_einvoice = row.get('e-invoice', 'N/A')
                aq_amount = row.get('TOTAL_COST', 'N/A')
                print(f"  المطابقة {i+1}: مبلغ={aq_amount}, فرق={diff:.2f}, e-invoice='{aq_einvoice}'")
    
    # البحث عن e-invoice المتوقع
    expected_einvoice = "K583BVYQGZX80XBCAWGX1K10"
    print(f"\n🔍 البحث عن e-invoice المتوقع: {expected_einvoice}")
    print("-" * 50)
    
    aq_einvoice_match = aq_df[aq_df['e-invoice'].astype(str) == expected_einvoice]
    if len(aq_einvoice_match) > 0:
        row = aq_einvoice_match.iloc[0]
        print(f"تم العثور على e-invoice في AQ:")
        print(f"  الرقم الضريبي: {row.get('الرقم الضريبى للبائع', 'N/A')}")
        print(f"  reference: {row.get('reference', 'N/A')}")
        print(f"  المبلغ: {row.get('TOTAL_COST', 'N/A')}")
        print(f"  البائع: {row.get('إسم البائع', 'N/A')}")
    else:
        print("❌ لم يتم العثور على e-invoice المتوقع في شيت AQ")

def main():
    file_path = "System_Hotels.xlsx"
    debug_reference_218071(file_path)

if __name__ == "__main__":
    main()
