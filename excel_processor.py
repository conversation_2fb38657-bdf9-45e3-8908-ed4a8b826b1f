import pandas as pd
import numpy as np
from openpyxl import load_workbook
import time

def process_hotels_data(file_path):
    """
    معالجة بيانات الفنادق وملء عمود e-invoice بكفاءة عالية
    """
    print("جاري قراءة ملف Excel...")
    
    # قراءة الشيتين
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', header=2)  # البيانات تبدأ من الصف 3
    aq_df = pd.read_excel(file_path, sheet_name='AQ', header=3)  # البيانات تبدأ من الصف 4
    
    print(f"تم قراءة {len(hotels_df)} صف من شيت hotels")
    print(f"تم قراءة {len(aq_df)} صف من شيت AQ")
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()
    
    # طباعة أسماء الأعمدة للتأكد
    print("\nأعمدة شيت hotels:")
    print(hotels_df.columns.tolist())
    print("\nأعمدة شيت AQ:")
    print(aq_df.columns.tolist())
    
    # إنشاء عمود e-invoice إذا لم يكن موجوداً
    if 'e-invoice' not in hotels_df.columns:
        hotels_df['e-invoice'] = ''
    
    # تحويل الأعمدة المطلوبة للمطابقة
    # افتراض أن عمود التاريخ في hotels هو 'checkin' وفي AQ هو العمود D
    # وأن عمود المبلغ في hotels هو 'TOTAL_COST' وفي AQ هو العمود H
    
    # تنظيف البيانات وإزالة القيم الفارغة
    aq_df = aq_df.dropna(subset=[aq_df.columns[2]])  # عمود C (e-invoice)
    
    print("جاري معالجة البيانات...")
    start_time = time.time()
    
    # إنشاء مجموعة للتتبع السريع للقيم المستخدمة
    used_einvoices = set()
    
    # معالجة كل صف في hotels
    for idx, hotel_row in hotels_df.iterrows():
        if pd.notna(hotels_df.at[idx, 'e-invoice']) and hotels_df.at[idx, 'e-invoice'] != '':
            # إذا كان e-invoice موجود بالفعل، أضفه للمجموعة المستخدمة
            used_einvoices.add(hotels_df.at[idx, 'e-invoice'])
            continue
            
        # الحصول على قيم المطابقة من الصف الحالي
        hotel_date = hotel_row.get('checkin', '')  # قد تحتاج لتعديل اسم العمود
        hotel_amount = hotel_row.get('TOTAL_COST', 0)  # قد تحتاج لتعديل اسم العمود
        hotel_supplier = hotel_row.get('SUPPLIERNAME', '')  # للمطابقة الإجبارية
        
        # البحث عن مطابقة إجبارية (نفس التاريخ ونفس المورد)
        mandatory_match = aq_df[
            (~aq_df.iloc[:, 2].isin(used_einvoices)) &  # عمود C غير مستخدم
            (aq_df.iloc[:, 3].astype(str).str.contains(str(hotel_date)[:7], na=False)) &  # عمود D يحتوي على نفس الشهر/السنة
            (aq_df.iloc[:, 5].astype(str).str.contains(str(hotel_supplier), na=False))  # عمود F يحتوي على نفس المورد
        ]
        
        if not mandatory_match.empty:
            # استخدام أول مطابقة إجبارية
            einvoice_value = mandatory_match.iloc[0, 2]  # عمود C
            hotels_df.at[idx, 'e-invoice'] = einvoice_value
            used_einvoices.add(einvoice_value)
            continue
        
        # البحث عن مطابقة اختيارية (نفس التاريخ ونفس المبلغ تقريباً)
        optional_match = aq_df[
            (~aq_df.iloc[:, 2].isin(used_einvoices)) &  # عمود C غير مستخدم
            (aq_df.iloc[:, 3].astype(str).str.contains(str(hotel_date)[:7], na=False)) &  # عمود D يحتوي على نفس الشهر/السنة
            (abs(pd.to_numeric(aq_df.iloc[:, 7], errors='coerce') - hotel_amount) < 1)  # عمود H قريب من المبلغ
        ]
        
        if not optional_match.empty:
            # استخدام أول مطابقة اختيارية
            einvoice_value = optional_match.iloc[0, 2]  # عمود C
            hotels_df.at[idx, 'e-invoice'] = einvoice_value
            used_einvoices.add(einvoice_value)
    
    processing_time = time.time() - start_time
    print(f"تم الانتهاء من المعالجة في {processing_time:.2f} ثانية")
    
    # حفظ النتائج
    output_file = file_path.replace('.xlsx', '_processed.xlsx')
    
    print("جاري حفظ النتائج...")
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        hotels_df.to_excel(writer, sheet_name='hotels', index=False, startrow=2)
        aq_df.to_excel(writer, sheet_name='AQ', index=False, startrow=3)
    
    print(f"تم حفظ الملف المعالج: {output_file}")
    
    # إحصائيات
    filled_count = hotels_df['e-invoice'].notna().sum()
    total_count = len(hotels_df)
    print(f"تم ملء {filled_count} من أصل {total_count} صف ({filled_count/total_count*100:.1f}%)")
    
    return hotels_df, aq_df

def main():
    """
    الدالة الرئيسية
    """
    file_path = "System_Hotels.xlsx"
    
    try:
        hotels_df, aq_df = process_hotels_data(file_path)
        print("تم الانتهاء بنجاح!")
        
    except Exception as e:
        print(f"حدث خطأ: {str(e)}")
        print("تأكد من:")
        print("1. وجود ملف System_Hotels.xlsx")
        print("2. وجود شيتين بأسماء 'hotels' و 'AQ'")
        print("3. صحة أسماء الأعمدة")

if __name__ == "__main__":
    main()
