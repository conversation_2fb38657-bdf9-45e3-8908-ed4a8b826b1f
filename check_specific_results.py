import pandas as pd

def check_specific_results(file_path):
    """
    التحقق من النتائج للحالات المحددة
    """
    print("🔍 التحقق من النتائج للحالات المحددة")
    print("=" * 50)
    
    # قراءة الملف المعالج
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=2, header=0)
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    
    print(f"تم قراءة {len(hotels_df)} صف من الملف المعالج")
    
    # الحالات المحددة للتحقق
    test_cases = [
        {'reference': '4383666', 'description': 'Reference 4383666'},
        {'reference': 'TT25-EG/1937', 'description': 'Reference TT25-EG/1937'}
    ]
    
    for case in test_cases:
        reference = case['reference']
        description = case['description']
        
        print(f"\n🔍 فحص {description}")
        print("-" * 30)
        
        # البحث في النتائج
        matches = hotels_df[hotels_df['reference'].astype(str) == reference]
        
        print(f"تم العثور على {len(matches)} صف:")
        
        for i, (idx, row) in enumerate(matches.iterrows()):
            tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
            amount = row.get('TOTAL_COST', 'N/A')
            einvoice = row.get('e-invoice', 'N/A')
            
            # تحقق من وجود e-invoice
            has_einvoice = einvoice and str(einvoice).strip() != '' and str(einvoice).strip() != 'nan'
            status = "✅ تم ملؤه" if has_einvoice else "❌ لم يتم ملؤه"
            
            print(f"  الصف {i+1}: رقم ضريبي={tax_num}, مبلغ={amount}")
            print(f"           e-invoice='{einvoice}' {status}")
    
    # إحصائيات عامة
    print(f"\n📊 إحصائيات عامة:")
    
    # حساب الصفوف المملوءة
    filled_count = 0
    for idx, row in hotels_df.iterrows():
        einvoice = row.get('e-invoice', '')
        if einvoice and str(einvoice).strip() != '' and str(einvoice).strip() != 'nan':
            filled_count += 1
    
    total_count = len(hotels_df)
    success_rate = (filled_count / total_count) * 100
    
    print(f"   • إجمالي الصفوف: {total_count:,}")
    print(f"   • الصفوف المملوءة: {filled_count:,}")
    print(f"   • نسبة النجاح: {success_rate:.1f}%")

def main():
    file_path = "System_Hotels_processed_final_corrected.xlsx"
    check_specific_results(file_path)

if __name__ == "__main__":
    main()
