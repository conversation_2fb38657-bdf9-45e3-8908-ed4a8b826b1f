import pandas as pd
import numpy as np

def debug_matching_process(file_path):
    """
    تحليل مفصل لعملية المطابقة لفهم المشكلة
    """
    print("🔍 تحليل مفصل لعملية المطابقة")
    print("=" * 50)
    
    # قراءة البيانات
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=3, header=0)
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=3, header=0)
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()
    
    # إزالة الصفوف الفارغة من AQ
    aq_df = aq_df.dropna(subset=['e-invoice'])
    
    print(f"تم قراءة {len(hotels_df)} صف من hotels و {len(aq_df)} صف من AQ")
    
    # تحليل الصف الأول من hotels
    print("\n📋 تحليل الصف الأول من hotels:")
    first_hotel = hotels_df.iloc[0]
    hotel_tax_number = str(first_hotel.get('الرقم الضريبى للبائع', ''))
    hotel_reference = str(first_hotel.get('reference', ''))
    hotel_amount = pd.to_numeric(first_hotel.get('TOTAL_COST', 0), errors='coerce')
    
    print(f"  الرقم الضريبي: '{hotel_tax_number}' (نوع: {type(hotel_tax_number)})")
    print(f"  Reference: '{hotel_reference}' (نوع: {type(hotel_reference)})")
    print(f"  المبلغ: {hotel_amount} (نوع: {type(hotel_amount)})")
    
    # البحث في AQ عن نفس الرقم الضريبي
    print(f"\n🔍 البحث في AQ عن الرقم الضريبي: {hotel_tax_number}")
    
    matching_tax_rows = aq_df[aq_df['الرقم الضريبى للبائع'].astype(str) == hotel_tax_number]
    print(f"تم العثور على {len(matching_tax_rows)} صف بنفس الرقم الضريبي")
    
    if len(matching_tax_rows) > 0:
        print("\nالصفوف المطابقة:")
        for i, (idx, row) in enumerate(matching_tax_rows.iterrows()):
            aq_reference = str(row.get('reference', ''))
            aq_amount = pd.to_numeric(row.get('TOTAL_COST', 0), errors='coerce')
            aq_einvoice = str(row.get('e-invoice', ''))
            
            print(f"  الصف {i+1}: reference='{aq_reference}', مبلغ={aq_amount}, e-invoice='{aq_einvoice}'")
            
            # مقارنة reference
            if aq_reference == hotel_reference:
                print(f"    ✅ مطابقة reference!")
                amount_diff = abs(hotel_amount - aq_amount) if pd.notna(aq_amount) else float('inf')
                print(f"    فرق المبلغ: {amount_diff}")
    
    # تحليل عينة أكبر
    print(f"\n📊 تحليل عينة من أول 5 صفوف في hotels:")
    for i in range(min(5, len(hotels_df))):
        hotel_row = hotels_df.iloc[i]
        h_tax = str(hotel_row.get('الرقم الضريبى للبائع', ''))
        h_ref = str(hotel_row.get('reference', ''))
        h_amount = pd.to_numeric(hotel_row.get('TOTAL_COST', 0), errors='coerce')
        
        print(f"\nالصف {i+1} في hotels:")
        print(f"  رقم ضريبي: {h_tax}, reference: {h_ref}, مبلغ: {h_amount}")
        
        # البحث عن مطابقات في AQ
        matches = aq_df[
            (aq_df['الرقم الضريبى للبائع'].astype(str) == h_tax) &
            (aq_df['reference'].astype(str) == h_ref)
        ]
        
        print(f"  مطابقات في AQ: {len(matches)}")
        if len(matches) > 0:
            for j, (idx, match_row) in enumerate(matches.iterrows()):
                match_amount = pd.to_numeric(match_row.get('TOTAL_COST', 0), errors='coerce')
                match_einvoice = str(match_row.get('e-invoice', ''))
                amount_diff = abs(h_amount - match_amount) if pd.notna(match_amount) else float('inf')
                print(f"    المطابقة {j+1}: مبلغ={match_amount}, فرق={amount_diff:.2f}, e-invoice={match_einvoice}")
    
    # تحليل أنواع البيانات
    print(f"\n🔬 تحليل أنواع البيانات:")
    print("في hotels:")
    print(f"  الرقم الضريبى للبائع: {hotels_df['الرقم الضريبى للبائع'].dtype}")
    print(f"  reference: {hotels_df['reference'].dtype}")
    print(f"  TOTAL_COST: {hotels_df['TOTAL_COST'].dtype}")
    
    print("في AQ:")
    print(f"  الرقم الضريبى للبائع: {aq_df['الرقم الضريبى للبائع'].dtype}")
    print(f"  reference: {aq_df['reference'].dtype}")
    print(f"  TOTAL_COST: {aq_df['TOTAL_COST'].dtype}")

def main():
    file_path = "System_Hotels.xlsx"
    debug_matching_process(file_path)

if __name__ == "__main__":
    main()
