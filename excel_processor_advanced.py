import pandas as pd
import numpy as np
from openpyxl import load_workbook
import time
from collections import defaultdict

def process_hotels_data_advanced(file_path):
    """
    معالجة بيانات الفنادق بأداء محسن باستخدام indexing وhashing
    """
    print("جاري قراءة ملف Excel...")

    # قراءة الشيتين - البيانات تبدأ من الصف 3 في hotels والصف 4 في AQ
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=2)
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=2)
    
    print(f"تم قراءة {len(hotels_df)} صف من شيت hotels")
    print(f"تم قراءة {len(aq_df)} صف من شيت AQ")
    
    # تنظيف أسماء الأعمدة وإزالة المسافات
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()

    # تنظيف البيانات
    aq_df = aq_df.dropna(subset=[aq_df.columns[2]])  # إزالة الصفوف بدون e-invoice

    # إنشاء عمود e-invoice إذا لم يكن موجوداً
    if 'e-invoice' not in hotels_df.columns:
        hotels_df['e-invoice'] = ''
    
    print("جاري إنشاء فهارس للبحث السريع...")
    
    # إنشاء فهارس للبحث السريع
    # فهرس للمطابقة الإجبارية (تاريخ + مورد)
    mandatory_index = defaultdict(list)
    # فهرس للمطابقة الاختيارية (تاريخ + مبلغ مقرب)
    optional_index = defaultdict(list)
    
    for idx, row in aq_df.iterrows():
        einvoice = row.iloc[2]  # عمود C
        date_str = str(row.iloc[3])[:7] if pd.notna(row.iloc[3]) else ''  # عمود D (أول 7 أحرف للشهر/السنة)
        supplier = str(row.iloc[5]) if pd.notna(row.iloc[5]) else ''  # عمود F
        amount = pd.to_numeric(row.iloc[7], errors='coerce')  # عمود H
        
        if pd.notna(amount):
            amount_rounded = round(amount)
        else:
            amount_rounded = 0
        
        # إضافة للفهرس الإجباري
        mandatory_key = f"{date_str}_{supplier}"
        mandatory_index[mandatory_key].append((idx, einvoice))
        
        # إضافة للفهرس الاختياري
        optional_key = f"{date_str}_{amount_rounded}"
        optional_index[optional_key].append((idx, einvoice))
    
    print("جاري معالجة البيانات...")
    start_time = time.time()
    
    used_einvoices = set()
    matched_count = 0
    
    for idx, hotel_row in hotels_df.iterrows():
        # تخطي الصفوف التي لديها e-invoice بالفعل
        if pd.notna(hotels_df.at[idx, 'e-invoice']) and hotels_df.at[idx, 'e-invoice'] != '':
            used_einvoices.add(hotels_df.at[idx, 'e-invoice'])
            continue
        
        # استخراج بيانات الفندق - استخدام أسماء الأعمدة الصحيحة
        hotel_date = str(hotel_row.get('CheckOut', ''))[:7] if pd.notna(hotel_row.get('CheckOut', '')) else ''
        hotel_supplier = str(hotel_row.get('SUPPLIERNAME', '')) if pd.notna(hotel_row.get('SUPPLIERNAME', '')) else ''
        hotel_amount = pd.to_numeric(hotel_row.get('TOTAL_COST', 0), errors='coerce')
        
        if pd.isna(hotel_amount):
            hotel_amount = 0
        
        hotel_amount_rounded = round(hotel_amount)
        
        # البحث في الفهرس الإجباري أولاً
        mandatory_key = f"{hotel_date}_{hotel_supplier}"
        found_match = False
        
        if mandatory_key in mandatory_index:
            for _, einvoice in mandatory_index[mandatory_key]:
                if einvoice not in used_einvoices:
                    hotels_df.at[idx, 'e-invoice'] = einvoice
                    used_einvoices.add(einvoice)
                    matched_count += 1
                    found_match = True
                    break

        # إذا لم نجد مطابقة إجبارية، ابحث في الفهرس الاختياري
        if not found_match:
            optional_key = f"{hotel_date}_{hotel_amount_rounded}"
            if optional_key in optional_index:
                for _, einvoice in optional_index[optional_key]:
                    if einvoice not in used_einvoices:
                        hotels_df.at[idx, 'e-invoice'] = einvoice
                        used_einvoices.add(einvoice)
                        matched_count += 1
                        break
    
    processing_time = time.time() - start_time
    print(f"تم الانتهاء من المعالجة في {processing_time:.2f} ثانية")
    print(f"تم العثور على {matched_count} مطابقة")
    
    # حفظ النتائج
    output_file = file_path.replace('.xlsx', '_processed_advanced.xlsx')
    
    print("جاري حفظ النتائج...")
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        hotels_df.to_excel(writer, sheet_name='hotels', index=False, startrow=2)
        aq_df.to_excel(writer, sheet_name='AQ', index=False, startrow=3)
    
    print(f"تم حفظ الملف المعالج: {output_file}")
    
    # إحصائيات مفصلة
    filled_count = (hotels_df['e-invoice'] != '').sum()
    total_count = len(hotels_df)
    print(f"تم ملء {filled_count} من أصل {total_count} صف ({filled_count/total_count*100:.1f}%)")
    
    return hotels_df, aq_df

def analyze_data_structure(file_path):
    """
    تحليل هيكل البيانات لفهم الأعمدة بشكل أفضل
    """
    print("جاري تحليل هيكل البيانات...")

    # قراءة أول بضعة صفوف لفهم الهيكل
    hotels_sample = pd.read_excel(file_path, sheet_name='hotels', skiprows=2, nrows=10)
    aq_sample = pd.read_excel(file_path, sheet_name='AQ', skiprows=2, nrows=10)
    
    print("\n=== تحليل شيت hotels ===")
    print("أسماء الأعمدة:")
    for i, col in enumerate(hotels_sample.columns):
        print(f"  {i}: {col}")
    
    print("\nعينة من البيانات:")
    print(hotels_sample.head(3))
    
    print("\n=== تحليل شيت AQ ===")
    print("أسماء الأعمدة:")
    for i, col in enumerate(aq_sample.columns):
        print(f"  {i}: {col}")
    
    print("\nعينة من البيانات:")
    print(aq_sample.head(3))

def main():
    """
    الدالة الرئيسية مع خيارات متعددة
    """
    file_path = "System_Hotels.xlsx"
    
    print("اختر العملية المطلوبة:")
    print("1. تحليل هيكل البيانات")
    print("2. معالجة البيانات (النسخة العادية)")
    print("3. معالجة البيانات (النسخة المحسنة)")
    print("4. تشغيل الكل")
    
    choice = input("أدخل رقم الخيار (1-4): ").strip()
    
    try:
        if choice == "1":
            analyze_data_structure(file_path)
        elif choice == "2":
            from excel_processor import process_hotels_data
            process_hotels_data(file_path)
        elif choice == "3":
            process_hotels_data_advanced(file_path)
        elif choice == "4":
            analyze_data_structure(file_path)
            print("\n" + "="*50)
            process_hotels_data_advanced(file_path)
        else:
            print("خيار غير صحيح")
            
    except Exception as e:
        print(f"حدث خطأ: {str(e)}")
        print("\nتأكد من:")
        print("1. وجود ملف System_Hotels.xlsx")
        print("2. وجود شيتين بأسماء 'hotels' و 'AQ'")
        print("3. تثبيت المكتبات المطلوبة: pip install pandas openpyxl")

if __name__ == "__main__":
    main()
