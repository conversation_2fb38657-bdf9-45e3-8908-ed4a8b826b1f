@echo off
chcp 65001 >nul
title Hotels Data Processor

echo.
echo Hotels Data Processor
echo ========================================
echo.

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if Excel file exists
if not exist "System_Hotels.xlsx" (
    echo Error: System_Hotels.xlsx file not found
    echo Make sure the file is in the same folder
    pause
    exit /b 1
)

echo Excel file found successfully
echo.

REM Check required libraries
echo Checking required libraries...
python -c "import pandas, openpyxl" >nul 2>&1
if errorlevel 1 (
    echo Required libraries not installed
    echo Installing libraries...
    pip install pandas openpyxl
    if errorlevel 1 (
        echo Failed to install libraries
        pause
        exit /b 1
    )
)

echo All libraries are available
echo.

REM Run the processor
echo Starting processing...
python excel_processor_final_corrected.py

echo.
echo Processing completed!
pause
