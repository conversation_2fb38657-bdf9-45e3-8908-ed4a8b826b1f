import pandas as pd
import numpy as np
import time
from collections import defaultdict
import warnings

# تجاهل تحذيرات pandas
warnings.filterwarnings('ignore', category=FutureWarning)

def process_hotels_data_new(file_path):
    """
    معالجة بيانات الفنادق بالطريقة الصحيحة:
    1. فلتر بالرقم الضريبي للبائع
    2. البحث بـ reference للمطابقة
    3. إذا وُجد أكثر من reference، البحث بـ TOTAL_COST مع التقريب
    4. وضع e-invoice مع تجنب التكرار
    """
    print("🏨 معالج بيانات الفنادق - النسخة الجديدة")
    print("=" * 50)
    
    start_total_time = time.time()
    
    # قراءة الشيتين مع تحديد الرؤوس الصحيحة
    print("📖 جاري قراءة ملف Excel...")
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=3, header=0)
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=3, header=0)
    
    print(f"✅ تم قراءة {len(hotels_df):,} صف من شيت hotels")
    print(f"✅ تم قراءة {len(aq_df):,} صف من شيت AQ")
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()
    
    # طباعة أسماء الأعمدة للتأكد
    print("\n📋 أعمدة شيت hotels:")
    for i, col in enumerate(hotels_df.columns):
        print(f"  {i}: '{col}'")
    
    print("\n📋 أعمدة شيت AQ:")
    for i, col in enumerate(aq_df.columns):
        print(f"  {i}: '{col}'")
    
    # إزالة الصفوف الفارغة من AQ
    aq_df_original_count = len(aq_df)
    aq_df = aq_df.dropna(subset=['e-invoice'])
    print(f"\n🧹 بعد تنظيف البيانات: {len(aq_df):,} صف في شيت AQ (تم حذف {aq_df_original_count - len(aq_df):,} صف فارغ)")
    
    # تحويل عمود e-invoice إلى نص
    if 'e-invoice' not in hotels_df.columns:
        hotels_df['e-invoice'] = ''
    hotels_df['e-invoice'] = hotels_df['e-invoice'].astype(str)
    
    print("\n🔍 جاري إنشاء فهارس للبحث السريع...")
    
    # إنشاء فهرس للبحث بالرقم الضريبي
    tax_number_index = defaultdict(list)
    
    for idx, row in aq_df.iterrows():
        einvoice = str(row['e-invoice']).strip()
        # تحويل الرقم الضريبي إلى int لضمان التطابق
        tax_raw = row['الرقم الضريبى للبائع']
        if pd.notna(tax_raw):
            tax_number = str(int(float(tax_raw)))
        else:
            tax_number = ''
        reference = str(row['reference']) if pd.notna(row['reference']) else ''
        amount = pd.to_numeric(row['TOTAL_COST'], errors='coerce')
        
        # إضافة للفهرس بالرقم الضريبي
        tax_number_index[tax_number].append({
            'idx': idx,
            'einvoice': einvoice,
            'reference': reference,
            'amount': amount if pd.notna(amount) else 0
        })
    
    print(f"📊 تم إنشاء فهرس للبحث بـ {len(tax_number_index):,} رقم ضريبي مختلف")
    
    print("\n⚙️ جاري معالجة البيانات...")
    start_processing_time = time.time()
    
    # تتبع e-invoices المستخدمة لتجنب التكرار
    used_einvoices = set()
    matched_count = 0
    skipped_existing = 0
    no_match_count = 0
    
    # أولاً، جمع e-invoices الموجودة بالفعل
    for idx, hotel_row in hotels_df.iterrows():
        current_einvoice = str(hotels_df.at[idx, 'e-invoice']).strip()
        if current_einvoice and current_einvoice != 'nan' and current_einvoice != '':
            used_einvoices.add(current_einvoice)
            skipped_existing += 1
    
    print(f"📝 تم العثور على {skipped_existing:,} e-invoice موجود مسبقاً")
    
    # معالجة كل صف في hotels
    for idx, hotel_row in hotels_df.iterrows():
        # تخطي الصفوف التي لديها e-invoice بالفعل
        current_einvoice = str(hotels_df.at[idx, 'e-invoice']).strip()
        if current_einvoice and current_einvoice != 'nan' and current_einvoice != '':
            continue
        
        # استخراج بيانات الفندق
        # تحويل الرقم الضريبي إلى int لإزالة .0
        hotel_tax_raw = hotel_row.get('الرقم الضريبى للبائع', '')
        if pd.notna(hotel_tax_raw):
            hotel_tax_number = str(int(float(hotel_tax_raw)))
        else:
            hotel_tax_number = ''

        hotel_reference = str(hotel_row.get('reference', '')) if pd.notna(hotel_row.get('reference', '')) else ''
        hotel_amount = pd.to_numeric(hotel_row.get('TOTAL_COST', 0), errors='coerce')
        
        if pd.isna(hotel_amount):
            hotel_amount = 0
        
        # البحث بالرقم الضريبي أولاً
        if hotel_tax_number in tax_number_index:
            candidates = tax_number_index[hotel_tax_number]
            
            # البحث بـ reference
            reference_matches = [c for c in candidates if c['reference'] == hotel_reference and c['einvoice'] not in used_einvoices]
            
            if len(reference_matches) == 1:
                # إذا وُجد reference واحد فقط، استخدمه مباشرة
                match = reference_matches[0]
                hotels_df.at[idx, 'e-invoice'] = match['einvoice']
                used_einvoices.add(match['einvoice'])
                matched_count += 1
                
            elif len(reference_matches) > 1:
                # إذا وُجد أكثر من reference، ابحث بالمبلغ مع التقريب
                best_match = None
                min_difference = float('inf')
                
                for candidate in reference_matches:
                    # حساب الفرق مع التقريب
                    amount_diff = abs(round(hotel_amount, 2) - round(candidate['amount'], 2))
                    
                    if amount_diff < min_difference:
                        min_difference = amount_diff
                        best_match = candidate
                
                # استخدام أفضل مطابقة إذا كان الفرق صغير (أقل من 1)
                if best_match and min_difference <= 1.0:
                    hotels_df.at[idx, 'e-invoice'] = best_match['einvoice']
                    used_einvoices.add(best_match['einvoice'])
                    matched_count += 1
                else:
                    no_match_count += 1
            else:
                no_match_count += 1
        else:
            no_match_count += 1
    
    processing_time = time.time() - start_processing_time
    
    print(f"✅ تم الانتهاء من المعالجة في {processing_time:.2f} ثانية")
    print(f"🎯 تم العثور على {matched_count:,} مطابقة جديدة")
    print(f"⏭️ تم تخطي {skipped_existing:,} صف لديه e-invoice بالفعل")
    print(f"❌ لم يتم العثور على مطابقة لـ {no_match_count:,} صف")
    
    # حفظ النتائج
    output_file = file_path.replace('.xlsx', '_processed_new.xlsx')
    
    print(f"\n💾 جاري حفظ النتائج في: {output_file}")
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        hotels_df.to_excel(writer, sheet_name='hotels', index=False, startrow=2)
        aq_df.to_excel(writer, sheet_name='AQ', index=False, startrow=2)
    
    # إحصائيات نهائية
    filled_count = (hotels_df['e-invoice'].astype(str).str.strip() != '').sum()
    filled_count -= (hotels_df['e-invoice'].astype(str).str.strip() == 'nan').sum()
    total_count = len(hotels_df)
    
    total_time = time.time() - start_total_time
    
    print("\n" + "=" * 50)
    print("📊 إحصائيات نهائية:")
    print(f"   📈 تم ملء {filled_count:,} من أصل {total_count:,} صف ({filled_count/total_count*100:.1f}%)")
    print(f"   🆕 مطابقات جديدة: {matched_count:,}")
    print(f"   ⏱️ إجمالي وقت التنفيذ: {total_time:.2f} ثانية")
    print(f"   🚀 متوسط المعالجة: {total_count/total_time:.0f} صف/ثانية")
    print("=" * 50)
    print("✅ تم الانتهاء بنجاح!")
    
    return hotels_df, aq_df, {
        'total_rows': total_count,
        'filled_rows': filled_count,
        'new_matches': matched_count,
        'existing_matches': skipped_existing,
        'no_matches': no_match_count,
        'processing_time': total_time
    }

def analyze_sample_data(hotels_df, aq_df):
    """
    تحليل عينة من البيانات لفهم المطابقة
    """
    print("\n🔍 تحليل عينة من البيانات:")
    print("-" * 40)
    
    # عرض عينة من hotels
    print("عينة من شيت hotels:")
    for i in range(min(3, len(hotels_df))):
        row = hotels_df.iloc[i]
        tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
        reference = row.get('reference', 'N/A')
        amount = row.get('TOTAL_COST', 'N/A')
        print(f"  الصف {i+1}: رقم ضريبي={tax_num}, reference={reference}, مبلغ={amount}")
    
    print("\nعينة من شيت AQ:")
    for i in range(min(3, len(aq_df))):
        row = aq_df.iloc[i]
        tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
        reference = row.get('reference', 'N/A')
        amount = row.get('TOTAL_COST', 'N/A')
        einvoice = row.get('e-invoice', 'N/A')
        print(f"  الصف {i+1}: رقم ضريبي={tax_num}, reference={reference}, مبلغ={amount}, e-invoice={einvoice}")

def main():
    """
    الدالة الرئيسية
    """
    file_path = "System_Hotels.xlsx"
    
    try:
        hotels_df, aq_df, stats = process_hotels_data_new(file_path)
        
        # تحليل عينة من البيانات
        analyze_sample_data(hotels_df, aq_df)
        
        print("\n🎉 العملية اكتملت بنجاح!")
        print(f"📁 الملف المعالج: {file_path.replace('.xlsx', '_processed_new.xlsx')}")
        
        # تحليل النتائج
        if stats['no_matches'] > 0:
            print(f"\n💡 نصائح للتحسين:")
            print(f"   • يوجد {stats['no_matches']:,} صف لم يتم العثور على مطابقة له")
            print(f"   • تحقق من صحة الأرقام الضريبية")
            print(f"   • تحقق من صحة أرقام reference")
            print(f"   • تحقق من دقة المبالغ")
        
    except FileNotFoundError:
        print("❌ خطأ: لم يتم العثور على ملف System_Hotels.xlsx")
        print("تأكد من وجود الملف في نفس المجلد")
    except Exception as e:
        print(f"❌ حدث خطأ: {str(e)}")
        import traceback
        traceback.print_exc()
        print("\n🔧 تأكد من:")
        print("1. وجود ملف System_Hotels.xlsx")
        print("2. وجود شيتين بأسماء 'hotels' و 'AQ'")
        print("3. تثبيت المكتبات: pip install pandas openpyxl")

if __name__ == "__main__":
    main()
