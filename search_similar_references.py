import pandas as pd
import numpy as np

def search_similar_references(file_path):
    """
    البحث عن references مشابهة لـ 218071
    """
    print("🔍 البحث عن references مشابهة")
    print("=" * 50)
    
    # قراءة البيانات
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=3, header=0)
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=3, header=0)
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()
    
    # إزالة الصفوف الفارغة من AQ
    aq_df = aq_df.dropna(subset=['e-invoice'])
    
    # البحث عن references تحتوي على 218071 أو مشابهة
    search_patterns = ["218071", "21807", "2180", "218", "1807", "8071"]
    
    print("🔍 البحث في شيت hotels:")
    print("-" * 30)
    
    for pattern in search_patterns:
        matches = hotels_df[hotels_df['reference'].astype(str).str.contains(pattern, na=False)]
        if len(matches) > 0:
            print(f"Pattern '{pattern}': {len(matches)} مطابقة")
            for i, (idx, row) in enumerate(matches.head(5).iterrows()):  # أول 5 فقط
                ref = row.get('reference', 'N/A')
                supplier = row.get('SUPPLIERNAME', 'N/A')
                amount = row.get('TOTAL_COST', 'N/A')
                print(f"  {i+1}. reference='{ref}', supplier='{supplier}', amount={amount}")
            if len(matches) > 5:
                print(f"  ... و {len(matches)-5} مطابقة أخرى")
            print()
    
    print("🔍 البحث في شيت AQ:")
    print("-" * 30)
    
    for pattern in search_patterns:
        matches = aq_df[aq_df['reference'].astype(str).str.contains(pattern, na=False)]
        if len(matches) > 0:
            print(f"Pattern '{pattern}': {len(matches)} مطابقة")
            for i, (idx, row) in enumerate(matches.head(5).iterrows()):  # أول 5 فقط
                ref = row.get('reference', 'N/A')
                supplier = row.get('إسم البائع', 'N/A')
                amount = row.get('TOTAL_COST', 'N/A')
                einvoice = row.get('e-invoice', 'N/A')
                print(f"  {i+1}. reference='{ref}', supplier='{supplier}', amount={amount}, e-invoice='{einvoice}'")
            if len(matches) > 5:
                print(f"  ... و {len(matches)-5} مطابقة أخرى")
            print()
    
    # البحث عن e-invoice المتوقع
    expected_einvoice = "K583BVYQGZX80XBCAWGX1K10"
    print(f"🔍 البحث عن e-invoice: {expected_einvoice}")
    print("-" * 50)
    
    aq_einvoice_match = aq_df[aq_df['e-invoice'].astype(str) == expected_einvoice]
    if len(aq_einvoice_match) > 0:
        row = aq_einvoice_match.iloc[0]
        print(f"تم العثور على e-invoice في AQ:")
        print(f"  الرقم الضريبي: {row.get('الرقم الضريبى للبائع', 'N/A')}")
        print(f"  reference: {row.get('reference', 'N/A')}")
        print(f"  المبلغ: {row.get('TOTAL_COST', 'N/A')}")
        print(f"  البائع: {row.get('إسم البائع', 'N/A')}")
        
        # البحث عن مطابقة في hotels
        aq_ref = str(row.get('reference', ''))
        aq_tax = str(row.get('الرقم الضريبى للبائع', ''))
        aq_amount = row.get('TOTAL_COST', 0)
        
        print(f"\n🔍 البحث عن مطابقة في hotels:")
        print(f"  reference: {aq_ref}")
        print(f"  رقم ضريبي: {aq_tax}")
        print(f"  مبلغ: {aq_amount}")
        
        # البحث بـ reference
        hotels_ref_match = hotels_df[hotels_df['reference'].astype(str) == aq_ref]
        print(f"  مطابقات بـ reference: {len(hotels_ref_match)}")
        
        if len(hotels_ref_match) > 0:
            for i, (idx, row) in enumerate(hotels_ref_match.iterrows()):
                hotel_tax = row.get('الرقم الضريبى للبائع', 'N/A')
                hotel_amount = row.get('TOTAL_COST', 'N/A')
                hotel_einvoice = row.get('e-invoice', 'N/A')
                print(f"    {i+1}. رقم ضريبي={hotel_tax}, مبلغ={hotel_amount}, e-invoice='{hotel_einvoice}'")
        
        # البحث بالرقم الضريبي
        hotels_tax_match = hotels_df[hotels_df['الرقم الضريبى للبائع'].astype(str) == aq_tax]
        print(f"  مطابقات بالرقم الضريبي: {len(hotels_tax_match)}")
        
        if len(hotels_tax_match) > 0:
            print("    أول 5 مطابقات:")
            for i, (idx, row) in enumerate(hotels_tax_match.head(5).iterrows()):
                hotel_ref = row.get('reference', 'N/A')
                hotel_amount = row.get('TOTAL_COST', 'N/A')
                hotel_einvoice = row.get('e-invoice', 'N/A')
                print(f"      {i+1}. reference={hotel_ref}, مبلغ={hotel_amount}, e-invoice='{hotel_einvoice}'")
    else:
        print("❌ لم يتم العثور على e-invoice المتوقع في شيت AQ")
    
    # البحث عن أي e-invoice يبدأ بـ K583
    print(f"\n🔍 البحث عن e-invoices تبدأ بـ K583:")
    print("-" * 40)
    
    k583_matches = aq_df[aq_df['e-invoice'].astype(str).str.startswith('K583', na=False)]
    print(f"تم العثور على {len(k583_matches)} e-invoice يبدأ بـ K583")
    
    if len(k583_matches) > 0:
        for i, (idx, row) in enumerate(k583_matches.head(10).iterrows()):
            ref = row.get('reference', 'N/A')
            einvoice = row.get('e-invoice', 'N/A')
            amount = row.get('TOTAL_COST', 'N/A')
            print(f"  {i+1}. reference='{ref}', e-invoice='{einvoice}', amount={amount}")

def main():
    file_path = "System_Hotels.xlsx"
    search_similar_references(file_path)

if __name__ == "__main__":
    main()
