🏨 معالج بيانات الفنادق - تعليمات سريعة
===============================================

📋 ما يفعله البرنامج:
- يقرأ ملف Excel يحتوي على بيانات الفنادق وفواتير AQ
- يطابق البيانات بالطريقة الصحيحة:
  1. فلتر بالرقم الضريبي للبائع
  2. البحث بـ reference للمطابقة
  3. إذا وُجد أكثر من reference، البحث بـ TOTAL_COST مع التقريب
  4. وضع e-invoice مع تجنب التكرار
- يوفر وقت كبير مقارنة بمعادلات Excel المعقدة

🚀 كيفية الاستخدام:

الطريقة الأولى (الأسهل):
1. تأكد من وجود ملف System_Hotels.xlsx في نفس المجلد
2. اضغط مرتين على ملف run.bat
3. انتظر حتى انتهاء المعالجة

الطريقة الثانية:
1. افتح Command Prompt أو PowerShell
2. انتقل لمجلد البرنامج
3. اكتب: python excel_processor_final_corrected.py
4. اضغط Enter

📊 النتائج المتوقعة:
- سرعة معالجة: حوالي 400-500 صف/ثانية
- نسبة نجاح: 60-70% من الصفوف
- مطابقات مباشرة بـ reference: ~85% من المطابقات
- مطابقات بالمبلغ: ~15% من المطابقات
- وقت المعالجة: أقل من دقيقة لآلاف الصفوف

📁 الملفات المُنتجة:
- System_Hotels_processed_final_corrected.xlsx (الملف المعالج)

🔧 متطلبات النظام:
- Python 3.6 أو أحدث
- مكتبات: pandas, openpyxl (يتم تثبيتها تلقائياً)

❓ في حالة المشاكل:
1. تأكد من تثبيت Python
2. تأكد من وجود ملف Excel في المجلد الصحيح
3. تأكد من أن الملف يحتوي على شيتين: hotels و AQ
4. تأكد من إغلاق ملف Excel قبل التشغيل

📞 للدعم:
راجع ملف README.md للتفاصيل الكاملة
