import pandas as pd
import numpy as np

def debug_C218071(file_path):
    """
    تحليل مفصل لـ reference C218071
    """
    print("🔍 تحليل مفصل لـ reference C218071")
    print("=" * 60)
    
    # قراءة البيانات
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=3, header=0)
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=3, header=0)
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()
    
    # إزالة الصفوف الفارغة من AQ
    aq_df = aq_df.dropna(subset=['e-invoice'])
    
    reference = "C218071"
    
    print(f"🔍 تحليل reference: {reference}")
    print("-" * 40)
    
    # في شيت hotels
    hotels_matches = hotels_df[hotels_df['reference'].astype(str) == reference]
    print(f"📋 في شيت hotels: تم العثور على {len(hotels_matches)} صف")
    
    if len(hotels_matches) > 0:
        for i, (idx, row) in enumerate(hotels_matches.iterrows()):
            tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
            amount = row.get('TOTAL_COST', 'N/A')
            einvoice = row.get('e-invoice', 'N/A')
            supplier = row.get('SUPPLIERNAME', 'N/A')
            
            print(f"  الصف {i+1}:")
            print(f"    الرقم الضريبي: {tax_num}")
            print(f"    المورد: {supplier}")
            print(f"    المبلغ: {amount}")
            print(f"    e-invoice الحالي: '{einvoice}'")
            
            # تحويل الرقم الضريبي للمطابقة
            if pd.notna(tax_num):
                tax_num_str = str(int(float(tax_num)))
            else:
                tax_num_str = ''
            
            print(f"    الرقم الضريبي المحول: {tax_num_str}")
    
    # في شيت AQ
    print(f"\n📋 في شيت AQ: البحث عن reference={reference}")
    aq_ref_matches = aq_df[aq_df['reference'].astype(str) == reference]
    print(f"تم العثور على {len(aq_ref_matches)} صف")
    
    if len(aq_ref_matches) > 0:
        for i, (idx, row) in enumerate(aq_ref_matches.iterrows()):
            tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
            amount = row.get('TOTAL_COST', 'N/A')
            einvoice = row.get('e-invoice', 'N/A')
            supplier = row.get('إسم البائع', 'N/A')
            
            print(f"  الصف {i+1}:")
            print(f"    الرقم الضريبي: {tax_num}")
            print(f"    البائع: {supplier}")
            print(f"    المبلغ: {amount}")
            print(f"    e-invoice: '{einvoice}'")
    
    # تحليل سبب عدم المطابقة
    if len(hotels_matches) > 0 and len(aq_ref_matches) > 0:
        print(f"\n🔍 تحليل سبب عدم المطابقة:")
        print("-" * 40)
        
        hotel_row = hotels_matches.iloc[0]
        aq_row = aq_ref_matches.iloc[0]
        
        hotel_tax_raw = hotel_row.get('الرقم الضريبى للبائع', '')
        if pd.notna(hotel_tax_raw):
            hotel_tax_number = str(int(float(hotel_tax_raw)))
        else:
            hotel_tax_number = ''
        
        aq_tax_number = str(aq_row.get('الرقم الضريبى للبائع', ''))
        
        hotel_amount = pd.to_numeric(hotel_row.get('TOTAL_COST', 0), errors='coerce')
        aq_amount = pd.to_numeric(aq_row.get('TOTAL_COST', 0), errors='coerce')
        
        print(f"Hotels - رقم ضريبي: '{hotel_tax_number}', مبلغ: {hotel_amount}")
        print(f"AQ     - رقم ضريبي: '{aq_tax_number}', مبلغ: {aq_amount}")
        
        # فحص مطابقة الرقم الضريبي
        tax_match = hotel_tax_number == aq_tax_number
        print(f"مطابقة الرقم الضريبي: {tax_match}")
        
        if not tax_match:
            print(f"  ❌ الأرقام الضريبية مختلفة!")
            print(f"     Hotels: '{hotel_tax_number}' (نوع: {type(hotel_tax_number)})")
            print(f"     AQ:     '{aq_tax_number}' (نوع: {type(aq_tax_number)})")
        
        # فحص مطابقة المبلغ
        if pd.notna(hotel_amount) and pd.notna(aq_amount):
            amount_diff = abs(float(hotel_amount) - float(aq_amount))
            max_allowed_diff = max(5.0, hotel_amount * 0.05)
            max_allowed_diff = min(max_allowed_diff, 50.0)
            
            amount_match = amount_diff <= max_allowed_diff
            print(f"فرق المبلغ: {amount_diff:.2f}")
            print(f"الحد الأقصى المسموح: {max_allowed_diff:.2f}")
            print(f"مطابقة المبلغ: {amount_match}")
            
            if not amount_match:
                print(f"  ❌ فرق المبلغ كبير جداً!")
        
        # تحليل المراحل الثلاث
        print(f"\n📊 تحليل المراحل الثلاث:")
        print(f"  المرحلة 1 (رقم ضريبي + reference + مبلغ): {'✅' if tax_match and amount_match else '❌'}")
        print(f"  المرحلة 2 (رقم ضريبي + reference): {'✅' if tax_match else '❌'}")
        print(f"  المرحلة 3 (رقم ضريبي + مبلغ): {'✅' if tax_match and amount_match else '❌'}")
    
    # فحص النتيجة الفعلية من الملف المعالج
    print(f"\n🔍 فحص النتيجة الفعلية:")
    print("-" * 30)
    
    try:
        # قراءة الملف المعالج الأحدث
        import glob
        processed_files = glob.glob("System_Hotels_processed_final_corrected_*.xlsx")
        if processed_files:
            latest_file = max(processed_files)
            print(f"قراءة الملف المعالج: {latest_file}")
            
            processed_df = pd.read_excel(latest_file, sheet_name='hotels', skiprows=2, header=0)
            processed_df.columns = processed_df.columns.str.strip()
            
            processed_matches = processed_df[processed_df['reference'].astype(str) == reference]
            print(f"النتائج المعالجة: {len(processed_matches)} صف")
            
            if len(processed_matches) > 0:
                for i, (idx, row) in enumerate(processed_matches.iterrows()):
                    einvoice = row.get('e-invoice', 'N/A')
                    amount = row.get('TOTAL_COST', 'N/A')
                    tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
                    
                    has_einvoice = einvoice and str(einvoice).strip() != '' and str(einvoice).strip() != 'nan'
                    status = "✅ تم ملؤه" if has_einvoice else "❌ لم يتم ملؤه"
                    
                    print(f"  الصف {i+1}: رقم ضريبي={tax_num}, مبلغ={amount}")
                    print(f"           e-invoice='{einvoice}' {status}")
        else:
            print("❌ لم يتم العثور على ملف معالج")
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف المعالج: {e}")

def main():
    file_path = "System_Hotels.xlsx"
    debug_C218071(file_path)

if __name__ == "__main__":
    main()
