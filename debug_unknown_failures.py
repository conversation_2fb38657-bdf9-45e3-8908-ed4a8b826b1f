import pandas as pd
import numpy as np

def debug_unknown_failures(file_path):
    """
    تحليل مفصل للحالات "غير الواضحة" التي يجب أن تنجح لكنها فشلت
    """
    print("🔍 تحليل الحالات غير الواضحة")
    print("=" * 50)
    
    # قراءة البيانات
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=3, header=0)
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=3, header=0)
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()
    
    # إزالة الصفوف الفارغة من AQ
    aq_df = aq_df.dropna(subset=['e-invoice'])
    
    # قراءة النتائج المعالجة
    try:
        import glob
        processed_files = glob.glob("System_Hotels_processed_final_corrected_*.xlsx")
        if processed_files:
            latest_file = max(processed_files)
            processed_df = pd.read_excel(latest_file, sheet_name='hotels', skiprows=2, header=0)
            processed_df.columns = processed_df.columns.str.strip()
        else:
            print("❌ لم يتم العثور على ملف معالج")
            return
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف المعالج: {e}")
        return
    
    # الحالات الفاشلة
    failed_matches = processed_df[
        (processed_df['e-invoice'].isna()) | 
        (processed_df['e-invoice'].astype(str) == 'nan') | 
        (processed_df['e-invoice'].astype(str) == '')
    ]
    
    print(f"إجمالي الحالات الفاشلة: {len(failed_matches):,}")
    
    # تتبع e-invoices المستخدمة
    used_einvoices = set()
    for idx, row in processed_df.iterrows():
        einvoice = row.get('e-invoice', '')
        if einvoice and str(einvoice).strip() != '' and str(einvoice).strip() != 'nan':
            used_einvoices.add(str(einvoice).strip())
    
    print(f"e-invoices المستخدمة: {len(used_einvoices):,}")
    
    # تحليل الحالات التي يجب أن تنجح
    suspicious_cases = []
    
    for idx, row in failed_matches.head(50).iterrows():  # فحص أول 50 حالة
        reference = row.get('reference', 'N/A')
        tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
        amount = row.get('TOTAL_COST', 'N/A')
        supplier = row.get('SUPPLIERNAME', 'N/A')
        
        if pd.isna(tax_num):
            continue
        
        tax_num_str = str(int(float(tax_num)))
        
        # البحث في AQ بالرقم الضريبي
        aq_tax_matches = aq_df[aq_df['الرقم الضريبى للبائع'].astype(str) == tax_num_str]
        
        if len(aq_tax_matches) == 0:
            continue
        
        # فحص المطابقة بـ reference
        aq_ref_matches = aq_tax_matches[aq_tax_matches['reference'].astype(str) == str(reference)]
        
        if len(aq_ref_matches) > 0:
            # يوجد مطابقة بـ reference - يجب أن تنجح في المرحلة 2
            aq_row = aq_ref_matches.iloc[0]
            aq_einvoice = str(aq_row.get('e-invoice', '')).strip()
            
            if aq_einvoice not in used_einvoices:
                suspicious_cases.append({
                    'type': 'reference_match_available',
                    'hotel_row': row,
                    'aq_row': aq_row,
                    'reason': 'يوجد مطابقة بـ reference و e-invoice غير مستخدم'
                })
        else:
            # فحص المطابقة بالمبلغ
            if pd.notna(amount):
                max_allowed_diff = max(5.0, float(amount) * 0.05)
                max_allowed_diff = min(max_allowed_diff, 50.0)
                
                for _, aq_row in aq_tax_matches.iterrows():
                    aq_amount = pd.to_numeric(aq_row.get('TOTAL_COST', 0), errors='coerce')
                    if pd.notna(aq_amount):
                        amount_diff = abs(float(amount) - float(aq_amount))
                        if amount_diff <= max_allowed_diff:
                            aq_einvoice = str(aq_row.get('e-invoice', '')).strip()
                            if aq_einvoice not in used_einvoices:
                                suspicious_cases.append({
                                    'type': 'amount_match_available',
                                    'hotel_row': row,
                                    'aq_row': aq_row,
                                    'amount_diff': amount_diff,
                                    'reason': 'يوجد مطابقة بالمبلغ و e-invoice غير مستخدم'
                                })
                                break
    
    print(f"\n🔍 الحالات المشبوهة (يجب أن تنجح): {len(suspicious_cases)}")
    print("-" * 50)
    
    for i, case in enumerate(suspicious_cases[:10]):  # أول 10 حالات
        hotel_row = case['hotel_row']
        aq_row = case['aq_row']
        
        print(f"\n❓ حالة مشبوهة #{i+1}: {case['reason']}")
        print(f"   Hotels:")
        print(f"     Reference: {hotel_row.get('reference', 'N/A')}")
        print(f"     المورد: {hotel_row.get('SUPPLIERNAME', 'N/A')}")
        print(f"     الرقم الضريبي: {hotel_row.get('الرقم الضريبى للبائع', 'N/A')}")
        print(f"     المبلغ: {hotel_row.get('TOTAL_COST', 'N/A')}")
        
        print(f"   AQ:")
        print(f"     Reference: {aq_row.get('reference', 'N/A')}")
        print(f"     البائع: {aq_row.get('إسم البائع', 'N/A')}")
        print(f"     الرقم الضريبي: {aq_row.get('الرقم الضريبى للبائع', 'N/A')}")
        print(f"     المبلغ: {aq_row.get('TOTAL_COST', 'N/A')}")
        print(f"     e-invoice: {aq_row.get('e-invoice', 'N/A')}")
        
        if 'amount_diff' in case:
            print(f"     فرق المبلغ: {case['amount_diff']:.2f}")
        
        # فحص ما إذا كان e-invoice مستخدم فعلاً
        aq_einvoice = str(aq_row.get('e-invoice', '')).strip()
        is_used = aq_einvoice in used_einvoices
        print(f"     e-invoice مستخدم: {'نعم' if is_used else 'لا'}")
        
        if is_used:
            # البحث عن من استخدمه
            users = processed_df[processed_df['e-invoice'].astype(str) == aq_einvoice]
            if len(users) > 0:
                user = users.iloc[0]
                print(f"     مستخدم بواسطة:")
                print(f"       Reference: {user.get('reference', 'N/A')}")
                print(f"       المورد: {user.get('SUPPLIERNAME', 'N/A')}")
                print(f"       المبلغ: {user.get('TOTAL_COST', 'N/A')}")
    
    # فحص مشكلة التكرار
    print(f"\n🔍 فحص مشكلة التكرار:")
    print("-" * 30)
    
    # البحث عن e-invoices مستخدمة أكثر من مرة
    einvoice_counts = {}
    for idx, row in processed_df.iterrows():
        einvoice = row.get('e-invoice', '')
        if einvoice and str(einvoice).strip() != '' and str(einvoice).strip() != 'nan':
            einvoice_str = str(einvoice).strip()
            if einvoice_str in einvoice_counts:
                einvoice_counts[einvoice_str] += 1
            else:
                einvoice_counts[einvoice_str] = 1
    
    duplicates = {k: v for k, v in einvoice_counts.items() if v > 1}
    print(f"e-invoices مكررة: {len(duplicates)}")
    
    if len(duplicates) > 0:
        print("أول 5 حالات تكرار:")
        for i, (einvoice, count) in enumerate(list(duplicates.items())[:5]):
            print(f"  {i+1}. {einvoice}: مستخدم {count} مرة")
            
            # عرض من استخدمه
            users = processed_df[processed_df['e-invoice'].astype(str) == einvoice]
            for j, (idx, user) in enumerate(users.iterrows()):
                print(f"     الاستخدام {j+1}: Reference={user.get('reference', 'N/A')}, المورد={user.get('SUPPLIERNAME', 'N/A')}")
    
    # إحصائيات نهائية
    print(f"\n📊 ملخص التحليل:")
    print("-" * 20)
    print(f"• إجمالي الحالات الفاشلة: {len(failed_matches):,}")
    print(f"• حالات يجب أن تنجح: {len(suspicious_cases)}")
    print(f"• e-invoices مكررة: {len(duplicates)}")
    print(f"• نسبة الحالات المشبوهة: {len(suspicious_cases)/len(failed_matches)*100:.1f}%")
    
    if len(suspicious_cases) > 0:
        print(f"\n💡 توصيات:")
        print(f"• يبدو أن هناك مشكلة في منطق المطابقة")
        print(f"• يجب مراجعة ترتيب المعالجة أو شروط المطابقة")
        print(f"• قد تكون هناك مشكلة في تتبع e-invoices المستخدمة")

def main():
    file_path = "System_Hotels.xlsx"
    debug_unknown_failures(file_path)

if __name__ == "__main__":
    main()
