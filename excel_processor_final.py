import pandas as pd
import numpy as np
import time
from collections import defaultdict
import warnings

# تجاهل تحذيرات pandas
warnings.filterwarnings('ignore', category=FutureWarning)

def process_hotels_data_final(file_path):
    """
    معالجة بيانات الفنادق - النسخة النهائية المحسنة
    """
    print("🏨 بدء معالجة بيانات الفنادق...")
    print("=" * 50)
    
    start_total_time = time.time()
    
    # قراءة الشيتين مع تحديد الرؤوس الصحيحة
    print("📖 جاري قراءة ملف Excel...")
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=3, header=0)
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=3, header=0)
    
    print(f"✅ تم قراءة {len(hotels_df):,} صف من شيت hotels")
    print(f"✅ تم قراءة {len(aq_df):,} صف من شيت AQ")
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()
    
    # إزالة الصفوف الفارغة من AQ
    aq_df_original_count = len(aq_df)
    aq_df = aq_df.dropna(subset=['e-invoice'])
    print(f"🧹 بعد تنظيف البيانات: {len(aq_df):,} صف في شيت AQ (تم حذف {aq_df_original_count - len(aq_df):,} صف فارغ)")
    
    # تحويل عمود e-invoice إلى نص لتجنب مشاكل النوع
    if 'e-invoice' not in hotels_df.columns:
        hotels_df['e-invoice'] = ''
    hotels_df['e-invoice'] = hotels_df['e-invoice'].astype(str)
    
    print("\n🔍 جاري إنشاء فهارس للبحث السريع...")
    
    # إنشاء فهارس للبحث السريع
    mandatory_index = defaultdict(list)  # تاريخ + مورد
    optional_index = defaultdict(list)   # تاريخ + مبلغ
    
    for idx, row in aq_df.iterrows():
        einvoice = str(row['e-invoice']).strip()
        
        # استخراج التاريخ (أول 7 أحرف للشهر/السنة)
        date_str = str(row['تاريخ الإصدار'])[:7] if pd.notna(row['تاريخ الإصدار']) else ''
        
        # استخراج اسم المورد
        supplier = str(row['إسم البائع']).strip() if pd.notna(row['إسم البائع']) else ''
        
        # استخراج المبلغ
        amount = pd.to_numeric(row['اجمالى الفاتورة بالعملة'], errors='coerce')
        amount_rounded = round(amount) if pd.notna(amount) else 0
        
        # إضافة للفهرس الإجباري (تاريخ + مورد)
        mandatory_key = f"{date_str}_{supplier}"
        mandatory_index[mandatory_key].append((idx, einvoice))
        
        # إضافة للفهرس الاختياري (تاريخ + مبلغ)
        optional_key = f"{date_str}_{amount_rounded}"
        optional_index[optional_key].append((idx, einvoice))
    
    print(f"📊 تم إنشاء {len(mandatory_index):,} مفتاح للمطابقة الإجبارية")
    print(f"📊 تم إنشاء {len(optional_index):,} مفتاح للمطابقة الاختيارية")
    
    print("\n⚙️ جاري معالجة البيانات...")
    start_processing_time = time.time()
    
    used_einvoices = set()
    matched_count = 0
    mandatory_matches = 0
    optional_matches = 0
    skipped_existing = 0
    
    for idx, hotel_row in hotels_df.iterrows():
        # تخطي الصفوف التي لديها e-invoice بالفعل
        current_einvoice = str(hotels_df.at[idx, 'e-invoice']).strip()
        if current_einvoice and current_einvoice != 'nan' and current_einvoice != '':
            used_einvoices.add(current_einvoice)
            skipped_existing += 1
            continue
        
        # استخراج بيانات الفندق
        hotel_date = str(hotel_row.get('CheckOut', ''))[:7] if pd.notna(hotel_row.get('CheckOut', '')) else ''
        hotel_supplier = str(hotel_row.get('SUPPLIERNAME', '')).strip() if pd.notna(hotel_row.get('SUPPLIERNAME', '')) else ''
        hotel_amount = pd.to_numeric(hotel_row.get('TOTAL_COST', 0), errors='coerce')
        
        if pd.isna(hotel_amount):
            hotel_amount = 0
        hotel_amount_rounded = round(hotel_amount)
        
        # البحث في الفهرس الإجباري أولاً (تاريخ + مورد)
        mandatory_key = f"{hotel_date}_{hotel_supplier}"
        found_match = False
        
        if mandatory_key in mandatory_index:
            for _, einvoice in mandatory_index[mandatory_key]:
                if einvoice not in used_einvoices:
                    hotels_df.at[idx, 'e-invoice'] = einvoice
                    used_einvoices.add(einvoice)
                    matched_count += 1
                    mandatory_matches += 1
                    found_match = True
                    break
        
        # إذا لم نجد مطابقة إجبارية، ابحث في الفهرس الاختياري (تاريخ + مبلغ)
        if not found_match:
            optional_key = f"{hotel_date}_{hotel_amount_rounded}"
            if optional_key in optional_index:
                for _, einvoice in optional_index[optional_key]:
                    if einvoice not in used_einvoices:
                        hotels_df.at[idx, 'e-invoice'] = einvoice
                        used_einvoices.add(einvoice)
                        matched_count += 1
                        optional_matches += 1
                        break
    
    processing_time = time.time() - start_processing_time
    
    print(f"✅ تم الانتهاء من المعالجة في {processing_time:.2f} ثانية")
    print(f"🎯 تم العثور على {matched_count:,} مطابقة جديدة")
    print(f"   📌 مطابقات إجبارية (تاريخ + مورد): {mandatory_matches:,}")
    print(f"   📌 مطابقات اختيارية (تاريخ + مبلغ): {optional_matches:,}")
    print(f"   ⏭️ تم تخطي {skipped_existing:,} صف لديه e-invoice بالفعل")
    
    # حفظ النتائج
    output_file = file_path.replace('.xlsx', '_processed_final.xlsx')
    
    print(f"\n💾 جاري حفظ النتائج في: {output_file}")
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        hotels_df.to_excel(writer, sheet_name='hotels', index=False, startrow=2)
        aq_df.to_excel(writer, sheet_name='AQ', index=False, startrow=2)
    
    # إحصائيات نهائية
    filled_count = (hotels_df['e-invoice'].astype(str).str.strip() != '').sum()
    filled_count -= (hotels_df['e-invoice'].astype(str).str.strip() == 'nan').sum()
    total_count = len(hotels_df)
    
    total_time = time.time() - start_total_time
    
    print("\n" + "=" * 50)
    print("📊 إحصائيات نهائية:")
    print(f"   📈 تم ملء {filled_count:,} من أصل {total_count:,} صف ({filled_count/total_count*100:.1f}%)")
    print(f"   ⏱️ إجمالي وقت التنفيذ: {total_time:.2f} ثانية")
    print(f"   🚀 متوسط المعالجة: {total_count/total_time:.0f} صف/ثانية")
    print("=" * 50)
    print("✅ تم الانتهاء بنجاح!")
    
    return hotels_df, aq_df, {
        'total_rows': total_count,
        'filled_rows': filled_count,
        'new_matches': matched_count,
        'mandatory_matches': mandatory_matches,
        'optional_matches': optional_matches,
        'processing_time': total_time
    }

def main():
    """
    الدالة الرئيسية
    """
    file_path = "System_Hotels.xlsx"
    
    try:
        hotels_df, aq_df, stats = process_hotels_data_final(file_path)
        
        print("\n🎉 العملية اكتملت بنجاح!")
        print(f"📁 الملف المعالج: {file_path.replace('.xlsx', '_processed_final.xlsx')}")
        
        # اقتراحات للتحسين
        if stats['filled_rows'] < stats['total_rows']:
            remaining = stats['total_rows'] - stats['filled_rows']
            print(f"\n💡 نصائح للتحسين:")
            print(f"   • يوجد {remaining:,} صف لم يتم ملؤه")
            print(f"   • تحقق من تطابق أسماء الموردين")
            print(f"   • تحقق من تنسيق التواريخ")
            print(f"   • تحقق من دقة المبالغ")
        
    except FileNotFoundError:
        print("❌ خطأ: لم يتم العثور على ملف System_Hotels.xlsx")
        print("تأكد من وجود الملف في نفس المجلد")
    except Exception as e:
        print(f"❌ حدث خطأ: {str(e)}")
        import traceback
        traceback.print_exc()
        print("\n🔧 تأكد من:")
        print("1. وجود ملف System_Hotels.xlsx")
        print("2. وجود شيتين بأسماء 'hotels' و 'AQ'")
        print("3. تثبيت المكتبات: pip install pandas openpyxl")

if __name__ == "__main__":
    main()
