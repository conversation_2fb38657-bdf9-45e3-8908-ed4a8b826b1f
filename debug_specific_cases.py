import pandas as pd
import numpy as np

def debug_specific_references(file_path):
    """
    تحليل مفصل للحالات المحددة التي لا تظهر فيها المطابقات
    """
    print("🔍 تحليل مفصل للحالات المحددة")
    print("=" * 60)
    
    # قراءة البيانات
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=3, header=0)
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=3, header=0)
    
    # تنظيف أسماء الأعمدة
    hotels_df.columns = hotels_df.columns.str.strip()
    aq_df.columns = aq_df.columns.str.strip()
    
    # إزالة الصفوف الفارغة من AQ
    aq_df = aq_df.dropna(subset=['e-invoice'])
    
    print(f"تم قراءة {len(hotels_df)} صف من hotels و {len(aq_df)} صف من AQ")
    
    # الحالات المحددة للتحقق
    test_cases = [
        {'reference': '4383666', 'description': 'Reference 4383666'},
        {'reference': 'TT25-EG/1937', 'description': 'Reference TT25-EG/1937'}
    ]
    
    for case in test_cases:
        reference = case['reference']
        description = case['description']
        
        print(f"\n🔍 تحليل {description}")
        print("-" * 40)
        
        # البحث في شيت hotels
        hotels_matches = hotels_df[hotels_df['reference'].astype(str) == reference]
        print(f"📋 في شيت hotels: تم العثور على {len(hotels_matches)} صف")
        
        if len(hotels_matches) > 0:
            for i, (idx, row) in enumerate(hotels_matches.iterrows()):
                tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
                amount = row.get('TOTAL_COST', 'N/A')
                einvoice = row.get('e-invoice', 'N/A')
                
                print(f"  الصف {i+1}: رقم ضريبي={tax_num}, مبلغ={amount}, e-invoice حالي='{einvoice}'")
                
                # تحويل الرقم الضريبي للمطابقة
                if pd.notna(tax_num):
                    tax_num_str = str(int(float(tax_num)))
                else:
                    tax_num_str = ''
                
                # البحث في AQ عن نفس الرقم الضريبي و reference
                aq_matches = aq_df[
                    (aq_df['الرقم الضريبى للبائع'].astype(str) == tax_num_str) &
                    (aq_df['reference'].astype(str) == reference)
                ]
                
                print(f"    🔍 مطابقات في AQ: {len(aq_matches)}")
                
                if len(aq_matches) > 0:
                    for j, (aq_idx, aq_row) in enumerate(aq_matches.iterrows()):
                        aq_amount = aq_row.get('TOTAL_COST', 'N/A')
                        aq_einvoice = aq_row.get('e-invoice', 'N/A')
                        
                        if pd.notna(amount) and pd.notna(aq_amount):
                            amount_diff = abs(float(amount) - float(aq_amount))
                        else:
                            amount_diff = 'N/A'
                        
                        print(f"      المطابقة {j+1}: مبلغ={aq_amount}, فرق المبلغ={amount_diff}, e-invoice='{aq_einvoice}'")
                else:
                    print(f"    ❌ لا توجد مطابقات في AQ للرقم الضريبي {tax_num_str} و reference {reference}")
        
        # البحث في شيت AQ
        print(f"\n📋 في شيت AQ: البحث عن reference={reference}")
        aq_ref_matches = aq_df[aq_df['reference'].astype(str) == reference]
        print(f"تم العثور على {len(aq_ref_matches)} صف")
        
        if len(aq_ref_matches) > 0:
            for i, (idx, row) in enumerate(aq_ref_matches.iterrows()):
                tax_num = row.get('الرقم الضريبى للبائع', 'N/A')
                amount = row.get('TOTAL_COST', 'N/A')
                einvoice = row.get('e-invoice', 'N/A')
                
                print(f"  الصف {i+1}: رقم ضريبي={tax_num}, مبلغ={amount}, e-invoice='{einvoice}'")
    
    # تحليل شامل للمطابقات المفقودة
    print(f"\n📊 تحليل شامل للمطابقات المفقودة")
    print("-" * 50)
    
    missing_matches = 0
    total_checked = 0
    
    for idx, hotel_row in hotels_df.iterrows():
        if total_checked >= 100:  # تحليل أول 100 صف فقط للسرعة
            break
            
        total_checked += 1
        
        # استخراج بيانات الفندق
        hotel_tax_raw = hotel_row.get('الرقم الضريبى للبائع', '')
        if pd.notna(hotel_tax_raw):
            hotel_tax_number = str(int(float(hotel_tax_raw)))
        else:
            hotel_tax_number = ''
            
        hotel_reference = str(hotel_row.get('reference', '')) if pd.notna(hotel_row.get('reference', '')) else ''
        hotel_einvoice = str(hotel_row.get('e-invoice', ''))
        
        # تخطي الصفوف التي لديها e-invoice بالفعل
        if hotel_einvoice and hotel_einvoice != 'nan' and hotel_einvoice != '':
            continue
        
        # البحث في AQ
        aq_matches = aq_df[
            (aq_df['الرقم الضريبى للبائع'].astype(str) == hotel_tax_number) &
            (aq_df['reference'].astype(str) == hotel_reference)
        ]
        
        if len(aq_matches) > 0:
            missing_matches += 1
            if missing_matches <= 5:  # عرض أول 5 حالات مفقودة
                print(f"❌ مطابقة مفقودة #{missing_matches}:")
                print(f"   Hotels: رقم ضريبي={hotel_tax_number}, reference={hotel_reference}")
                print(f"   AQ matches: {len(aq_matches)}")
                for j, (aq_idx, aq_row) in enumerate(aq_matches.iterrows()):
                    aq_einvoice = aq_row.get('e-invoice', 'N/A')
                    print(f"     المطابقة {j+1}: e-invoice='{aq_einvoice}'")
    
    print(f"\n📈 ملخص التحليل:")
    print(f"   • تم فحص {total_checked} صف من hotels")
    print(f"   • تم العثور على {missing_matches} مطابقة مفقودة")
    
    if missing_matches > 0:
        print(f"\n💡 يبدو أن هناك مشكلة في الكود! سأقوم بإصلاحها.")

def main():
    file_path = "System_Hotels.xlsx"
    debug_specific_references(file_path)

if __name__ == "__main__":
    main()
