#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لمعالج بيانات الفنادق
Quick run for Hotels Data Processor
"""

import os
import sys

def main():
    """
    تشغيل سريع للمعالج
    """
    print("🏨 معالج بيانات الفنادق")
    print("=" * 40)
    
    # التحقق من وجود الملف
    if not os.path.exists("System_Hotels.xlsx"):
        print("❌ خطأ: لم يتم العثور على ملف System_Hotels.xlsx")
        print("تأكد من وضع الملف في نفس المجلد")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ تم العثور على ملف System_Hotels.xlsx")
    print("\nاختر نوع المعالجة:")
    print("1. المعالجة النهائية (الأفضل) 🌟")
    print("2. المعالجة المبسطة")
    print("3. تحليل البيانات فقط")
    print("4. الخروج")
    
    while True:
        try:
            choice = input("\nأدخل رقم الخيار (1-4): ").strip()
            
            if choice == "1":
                print("\n🚀 بدء المعالجة النهائية...")
                os.system("python excel_processor_final.py")
                break
            elif choice == "2":
                print("\n🚀 بدء المعالجة المبسطة...")
                os.system("python excel_processor_simple.py")
                break
            elif choice == "3":
                print("\n🔍 بدء تحليل البيانات...")
                os.system("python excel_processor_debug.py")
                break
            elif choice == "4":
                print("👋 وداعاً!")
                break
            else:
                print("❌ خيار غير صحيح، حاول مرة أخرى")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج")
            break
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")
            break
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
