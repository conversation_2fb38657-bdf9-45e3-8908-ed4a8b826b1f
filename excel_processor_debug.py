import pandas as pd
import numpy as np
import time
from collections import defaultdict

def analyze_excel_structure(file_path):
    """
    تحليل مفصل لهيكل ملف Excel
    """
    print("تحليل مفصل لهيكل ملف Excel...")
    
    # قراءة الشيتين بطرق مختلفة
    print("\n=== تحليل شيت hotels ===")
    
    # قراءة أول 10 صفوف بدون تخطي
    hotels_raw = pd.read_excel(file_path, sheet_name='hotels', nrows=10)
    print("البيانات الخام (أول 10 صفوف):")
    print(hotels_raw)
    
    # قراءة مع تخطي صفوف مختلفة
    for skip in [0, 1, 2, 3]:
        try:
            df = pd.read_excel(file_path, sheet_name='hotels', skiprows=skip, nrows=5)
            print(f"\nمع تخطي {skip} صف:")
            print("أسماء الأعمدة:", df.columns.tolist())
            print("أول صف:", df.iloc[0].tolist() if len(df) > 0 else "لا توجد بيانات")
        except Exception as e:
            print(f"خطأ مع تخطي {skip} صف: {e}")
    
    print("\n=== تحليل شيت AQ ===")
    
    # قراءة أول 10 صفوف بدون تخطي
    aq_raw = pd.read_excel(file_path, sheet_name='AQ', nrows=10)
    print("البيانات الخام (أول 10 صفوف):")
    print(aq_raw)
    
    # قراءة مع تخطي صفوف مختلفة
    for skip in [0, 1, 2, 3]:
        try:
            df = pd.read_excel(file_path, sheet_name='AQ', skiprows=skip, nrows=5)
            print(f"\nمع تخطي {skip} صف:")
            print("أسماء الأعمدة:", df.columns.tolist())
            print("أول صف:", df.iloc[0].tolist() if len(df) > 0 else "لا توجد بيانات")
        except Exception as e:
            print(f"خطأ مع تخطي {skip} صف: {e}")

def process_hotels_with_correct_headers(file_path):
    """
    معالجة البيانات مع تحديد الرؤوس الصحيحة
    """
    print("\n" + "="*60)
    print("بدء المعالجة مع الرؤوس الصحيحة...")
    
    # قراءة شيت hotels - الرؤوس في الصف 3 (index 2)
    hotels_df = pd.read_excel(file_path, sheet_name='hotels', skiprows=2)
    
    # قراءة شيت AQ - الرؤوس في الصف 3 (index 2) 
    aq_df = pd.read_excel(file_path, sheet_name='AQ', skiprows=2)
    
    print(f"تم قراءة {len(hotels_df)} صف من شيت hotels")
    print(f"تم قراءة {len(aq_df)} صف من شيت AQ")
    
    # طباعة أسماء الأعمدة الفعلية
    print("\nأعمدة شيت hotels:")
    for i, col in enumerate(hotels_df.columns):
        print(f"  {i}: '{col}'")
    
    print("\nأعمدة شيت AQ:")
    for i, col in enumerate(aq_df.columns):
        print(f"  {i}: '{col}'")
    
    # عرض عينة من البيانات
    print("\nعينة من بيانات hotels (أول 3 صفوف):")
    print(hotels_df.head(3))
    
    print("\nعينة من بيانات AQ (أول 3 صفوف):")
    print(aq_df.head(3))
    
    # تنظيف البيانات
    aq_df_clean = aq_df.dropna(subset=[aq_df.columns[2]])  # إزالة الصفوف بدون e-invoice
    print(f"\nبعد تنظيف AQ: {len(aq_df_clean)} صف")
    
    # إنشاء عمود e-invoice إذا لم يكن موجوداً
    if 'e-invoice' not in hotels_df.columns:
        hotels_df['e-invoice'] = ''
        print("تم إنشاء عمود e-invoice في شيت hotels")
    
    # تحليل البيانات للمطابقة
    print("\nتحليل بيانات المطابقة...")
    
    # عرض عينة من بيانات AQ للفهم
    print("عينة من بيانات AQ للمطابقة:")
    for i in range(min(5, len(aq_df_clean))):
        row = aq_df_clean.iloc[i]
        print(f"  الصف {i}: e-invoice={row.iloc[2]}, التاريخ={row.iloc[3]}, المورد={row.iloc[5]}, المبلغ={row.iloc[7]}")
    
    # عرض عينة من بيانات hotels للفهم
    print("\nعينة من بيانات hotels للمطابقة:")
    for i in range(min(5, len(hotels_df))):
        row = hotels_df.iloc[i]
        checkout_col = 'CheckOut' if 'CheckOut' in hotels_df.columns else hotels_df.columns[4]
        supplier_col = 'SUPPLIERNAME' if 'SUPPLIERNAME' in hotels_df.columns else hotels_df.columns[3]
        cost_col = 'TOTAL_COST' if 'TOTAL_COST' in hotels_df.columns else hotels_df.columns[6]
        
        print(f"  الصف {i}: التاريخ={row.get(checkout_col, 'N/A')}, المورد={row.get(supplier_col, 'N/A')}, المبلغ={row.get(cost_col, 'N/A')}")
    
    return hotels_df, aq_df_clean

def main():
    """
    الدالة الرئيسية للتحليل والمعالجة
    """
    file_path = "System_Hotels.xlsx"
    
    try:
        # تحليل الهيكل أولاً
        analyze_excel_structure(file_path)
        
        # معالجة البيانات
        hotels_df, aq_df = process_hotels_with_correct_headers(file_path)
        
        print("\n" + "="*60)
        print("تم الانتهاء من التحليل!")
        
    except Exception as e:
        print(f"حدث خطأ: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
